#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@Time        : 2025/6/30
<AUTHOR> Augment Agent
@File        : main_gui.py
@Description : 微信聊天记录解密工具 GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import json
import os
import sys
from multiprocessing import freeze_support
import traceback
from pathlib import Path

# 导入核心功能模块
try:
    from wxManager import Me, DatabaseConnection, MessageType
    from wxManager.decrypt import get_info_v4, get_info_v3
    from wxManager.decrypt.decrypt_dat import get_decode_code_v4
    from wxManager.decrypt import decrypt_v4, decrypt_v3
    from exporter.config import FileType
    from exporter import HtmlExporter, TxtExporter, DocxExporter, MarkdownExporter, ExcelExporter
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所有必要的依赖包")
    print("运行: pip install -r requirements.txt")
    input("按任意键退出...")
    sys.exit(1)


class WeChatDecryptGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("微信聊天记录解密工具 v3.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置窗口图标（如果存在）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 初始化变量
        self.wx_version = tk.StringVar(value="4.0")
        self.output_dir = tk.StringVar()
        self.db_dir = tk.StringVar()
        self.selected_contacts = []
        self.database_interface = None
        
        # 创建界面
        self.create_widgets()
        
        # 加载版本列表
        self.load_version_list()
    
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="微信聊天记录解密工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 微信版本选择
        version_frame = ttk.LabelFrame(main_frame, text="微信版本选择", padding="10")
        version_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Radiobutton(version_frame, text="微信 3.x 版本", 
                       variable=self.wx_version, value="3.x").grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(version_frame, text="微信 4.0 版本", 
                       variable=self.wx_version, value="4.0").grid(row=0, column=1, sticky=tk.W)
        
        # 操作按钮区域
        button_frame = ttk.LabelFrame(main_frame, text="操作功能", padding="10")
        button_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 解密数据库按钮
        self.decrypt_btn = ttk.Button(button_frame, text="1. 解密微信数据库", 
                                     command=self.decrypt_database, width=20)
        self.decrypt_btn.grid(row=0, column=0, padx=(0, 10), pady=5)
        
        # 查看联系人按钮
        self.contacts_btn = ttk.Button(button_frame, text="2. 查看联系人", 
                                      command=self.view_contacts, width=20, state="disabled")
        self.contacts_btn.grid(row=0, column=1, padx=(0, 10), pady=5)
        
        # 导出数据按钮
        self.export_btn = ttk.Button(button_frame, text="3. 导出聊天记录", 
                                    command=self.export_data, width=20, state="disabled")
        self.export_btn.grid(row=0, column=2, pady=5)
        
        # 数据库路径显示
        path_frame = ttk.LabelFrame(main_frame, text="数据库路径", padding="10")
        path_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        path_frame.columnconfigure(1, weight=1)
        
        ttk.Label(path_frame, text="数据库目录:").grid(row=0, column=0, sticky=tk.W)
        self.db_path_entry = ttk.Entry(path_frame, textvariable=self.db_dir, state="readonly")
        self.db_path_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 10))
        
        ttk.Button(path_frame, text="浏览", command=self.browse_db_dir).grid(row=0, column=2)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 底部按钮
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.grid(row=6, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(bottom_frame, text="清空日志", command=self.clear_log).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(bottom_frame, text="关于", command=self.show_about).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(bottom_frame, text="退出", command=self.root.quit).grid(row=0, column=2)
    
    def load_version_list(self):
        """加载微信版本列表"""
        try:
            version_list_path = 'wxManager/decrypt/version_list.json'
            if os.path.exists(version_list_path):
                with open(version_list_path, "r", encoding="utf-8") as f:
                    self.version_list = json.loads(f.read())
                self.log("版本列表加载成功")
            else:
                self.version_list = {}
                self.log("警告: 版本列表文件不存在")
        except Exception as e:
            self.version_list = {}
            self.log(f"加载版本列表失败: {e}")
    
    def log(self, message):
        """添加日志信息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def browse_db_dir(self):
        """浏览数据库目录"""
        directory = filedialog.askdirectory(title="选择数据库目录")
        if directory:
            self.db_dir.set(directory)
            self.check_database_connection()
    
    def check_database_connection(self):
        """检查数据库连接"""
        if not self.db_dir.get():
            return
        
        try:
            db_version = 4 if self.wx_version.get() == "4.0" else 3
            conn = DatabaseConnection(self.db_dir.get(), db_version)
            self.database_interface = conn.get_interface()
            
            if self.database_interface:
                self.contacts_btn.config(state="normal")
                self.export_btn.config(state="normal")
                self.log("数据库连接成功")
            else:
                self.contacts_btn.config(state="disabled")
                self.export_btn.config(state="disabled")
                self.log("数据库连接失败")
        except Exception as e:
            self.log(f"数据库连接错误: {e}")
    
    def decrypt_database(self):
        """解密微信数据库"""
        def decrypt_worker():
            try:
                self.progress.start()
                self.decrypt_btn.config(state="disabled")
                
                if self.wx_version.get() == "4.0":
                    self.log("开始解密微信4.0数据库...")
                    self.decrypt_v4_database()
                else:
                    self.log("开始解密微信3.x数据库...")
                    self.decrypt_v3_database()
                    
            except Exception as e:
                self.log(f"解密失败: {e}")
                self.log(f"错误详情: {traceback.format_exc()}")
            finally:
                self.progress.stop()
                self.decrypt_btn.config(state="normal")
        
        # 在新线程中执行解密操作
        threading.Thread(target=decrypt_worker, daemon=True).start()
    
    def decrypt_v4_database(self):
        """解密微信4.0数据库"""
        r_4 = get_info_v4()
        if not r_4:
            self.log("未找到微信4.0进程，请确保微信已登录")
            return
        
        for wx_info in r_4:
            self.log(f"找到微信用户: {wx_info.nick_name} ({wx_info.wxid})")
            
            me = Me()
            me.wx_dir = wx_info.wx_dir
            me.wxid = wx_info.wxid
            me.name = wx_info.nick_name
            me.xor_key = get_decode_code_v4(wx_info.wx_dir)
            
            output_dir = wx_info.wxid
            key = wx_info.key
            
            if not key:
                self.log('错误: 未找到密钥，请重启微信后再试')
                continue
            
            self.log(f"开始解密数据库到目录: {output_dir}")
            decrypt_v4.decrypt_db_files(key, src_dir=wx_info.wx_dir, dest_dir=output_dir)
            
            # 保存用户信息
            info_data = me.to_json()
            db_storage_path = os.path.join(output_dir, 'db_storage')
            os.makedirs(db_storage_path, exist_ok=True)
            
            with open(os.path.join(db_storage_path, 'info.json'), 'w', encoding='utf-8') as f:
                json.dump(info_data, f, ensure_ascii=False, indent=4)
            
            self.log(f'数据库解析成功，路径: {db_storage_path}')
            self.db_dir.set(db_storage_path)
            self.check_database_connection()
            break
    
    def decrypt_v3_database(self):
        """解密微信3.x数据库"""
        if not self.version_list:
            self.log("错误: 版本列表未加载")
            return
        
        r_3 = get_info_v3(self.version_list)
        if not r_3:
            self.log("未找到微信3.x进程，请确保微信已登录")
            return
        
        for wx_info in r_3:
            self.log(f"找到微信用户: {wx_info.nick_name} ({wx_info.wxid})")
            
            me = Me()
            me.wx_dir = wx_info.wx_dir
            me.wxid = wx_info.wxid
            me.name = wx_info.nick_name
            
            output_dir = wx_info.wxid
            key = wx_info.key
            
            if not key:
                self.log('错误: 未找到密钥，请重启微信后再试')
                continue
            
            self.log(f"开始解密数据库到目录: {output_dir}")
            decrypt_v3.decrypt_db_files(key, src_dir=wx_info.wx_dir, dest_dir=output_dir)
            
            # 保存用户信息
            info_data = me.to_json()
            msg_path = os.path.join(output_dir, 'Msg')
            os.makedirs(msg_path, exist_ok=True)
            
            with open(os.path.join(msg_path, 'info.json'), 'w', encoding='utf-8') as f:
                json.dump(info_data, f, ensure_ascii=False, indent=4)
            
            self.log(f'数据库解析成功，路径: {msg_path}')
            self.db_dir.set(msg_path)
            self.check_database_connection()
            break

    def view_contacts(self):
        """查看联系人"""
        if not self.database_interface:
            messagebox.showerror("错误", "请先解密数据库")
            return

        def contacts_worker():
            try:
                self.progress.start()
                self.log("正在加载联系人列表...")

                # 获取联系人列表
                contacts = self.database_interface.get_contact()

                if contacts:
                    self.log(f"找到 {len(contacts)} 个联系人")
                    self.show_contacts_window(contacts)
                else:
                    self.log("未找到联系人数据")

            except Exception as e:
                self.log(f"加载联系人失败: {e}")
                self.log(f"错误详情: {traceback.format_exc()}")
            finally:
                self.progress.stop()

        threading.Thread(target=contacts_worker, daemon=True).start()

    def show_contacts_window(self, contacts):
        """显示联系人选择窗口"""
        contacts_window = tk.Toplevel(self.root)
        contacts_window.title("选择联系人")
        contacts_window.geometry("600x400")
        contacts_window.transient(self.root)
        contacts_window.grab_set()

        # 创建联系人列表
        frame = ttk.Frame(contacts_window, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)

        # 搜索框
        search_frame = ttk.Frame(frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT)
        search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=search_var)
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        # 联系人列表
        list_frame = ttk.Frame(frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 创建Treeview
        columns = ('wxid', 'name', 'type')
        tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # 定义列标题
        tree.heading('wxid', text='微信ID')
        tree.heading('name', text='昵称')
        tree.heading('type', text='类型')

        # 设置列宽
        tree.column('wxid', width=200)
        tree.column('name', width=200)
        tree.column('type', width=100)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 填充联系人数据
        for contact in contacts:
            contact_type = "群聊" if contact.is_chatroom else "好友"
            tree.insert('', tk.END, values=(contact.wxid, contact.remark or contact.nickname, contact_type))

        # 搜索功能
        def search_contacts():
            search_text = search_var.get().lower()
            for item in tree.get_children():
                tree.delete(item)

            for contact in contacts:
                contact_type = "群聊" if contact.is_chatroom else "好友"
                name = contact.remark or contact.nickname or ""
                wxid = contact.wxid or ""

                if (search_text in name.lower() or
                    search_text in wxid.lower() or
                    search_text in contact_type.lower()):
                    tree.insert('', tk.END, values=(wxid, name, contact_type))

        search_var.trace('w', lambda *args: search_contacts())

        # 按钮框架
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        def select_contacts():
            selected_items = tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请选择至少一个联系人")
                return

            self.selected_contacts = []
            for item in selected_items:
                values = tree.item(item, 'values')
                self.selected_contacts.append(values[0])  # wxid

            self.log(f"已选择 {len(self.selected_contacts)} 个联系人")
            contacts_window.destroy()

        ttk.Button(button_frame, text="确定选择", command=select_contacts).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=contacts_window.destroy).pack(side=tk.RIGHT)

        # 全选/取消全选
        def select_all():
            for item in tree.get_children():
                tree.selection_add(item)

        def deselect_all():
            tree.selection_remove(tree.selection())

        ttk.Button(button_frame, text="全选", command=select_all).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="取消全选", command=deselect_all).pack(side=tk.LEFT, padx=(5, 0))

    def export_data(self):
        """导出聊天记录"""
        if not self.database_interface:
            messagebox.showerror("错误", "请先解密数据库")
            return

        if not self.selected_contacts:
            messagebox.showwarning("警告", "请先选择要导出的联系人")
            return

        # 选择导出格式
        export_window = tk.Toplevel(self.root)
        export_window.title("选择导出格式")
        export_window.geometry("400x300")
        export_window.transient(self.root)
        export_window.grab_set()

        frame = ttk.Frame(export_window, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(frame, text="请选择导出格式:", font=("Arial", 12)).pack(pady=(0, 20))

        export_format = tk.StringVar(value="HTML")

        formats = [
            ("HTML格式 (推荐)", "HTML"),
            ("TXT文本格式", "TXT"),
            ("Word文档格式", "DOCX"),
            ("Markdown格式", "MARKDOWN"),
            ("Excel表格格式", "XLSX")
        ]

        for text, value in formats:
            ttk.Radiobutton(frame, text=text, variable=export_format, value=value).pack(anchor=tk.W, pady=5)

        # 输出目录选择
        ttk.Label(frame, text="输出目录:", font=("Arial", 10)).pack(anchor=tk.W, pady=(20, 5))

        output_frame = ttk.Frame(frame)
        output_frame.pack(fill=tk.X, pady=(0, 20))

        output_path = tk.StringVar(value=os.path.join(os.getcwd(), "export"))
        ttk.Entry(output_frame, textvariable=output_path).pack(side=tk.LEFT, fill=tk.X, expand=True)

        def browse_output():
            directory = filedialog.askdirectory(title="选择输出目录")
            if directory:
                output_path.set(directory)

        ttk.Button(output_frame, text="浏览", command=browse_output).pack(side=tk.RIGHT, padx=(5, 0))

        # 按钮
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X)

        def start_export():
            export_window.destroy()
            self.perform_export(export_format.get(), output_path.get())

        ttk.Button(button_frame, text="开始导出", command=start_export).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=export_window.destroy).pack(side=tk.RIGHT)

    def perform_export(self, format_type, output_path):
        """执行导出操作"""
        def export_worker():
            try:
                self.progress.start()
                self.export_btn.config(state="disabled")

                os.makedirs(output_path, exist_ok=True)
                self.log(f"开始导出 {len(self.selected_contacts)} 个联系人的聊天记录...")
                self.log(f"导出格式: {format_type}")
                self.log(f"输出目录: {output_path}")

                db_version = 4 if self.wx_version.get() == "4.0" else 3

                for i, wxid in enumerate(self.selected_contacts):
                    self.log(f"正在导出 {wxid} ({i+1}/{len(self.selected_contacts)})")

                    try:
                        if format_type == "HTML":
                            exporter = HtmlExporter(self.db_dir.get(), wxid, output_path, db_version)
                        elif format_type == "TXT":
                            exporter = TxtExporter(self.db_dir.get(), wxid, output_path, db_version)
                        elif format_type == "DOCX":
                            exporter = DocxExporter(self.db_dir.get(), wxid, output_path, db_version)
                        elif format_type == "MARKDOWN":
                            exporter = MarkdownExporter(self.db_dir.get(), wxid, output_path, db_version)
                        elif format_type == "XLSX":
                            exporter = ExcelExporter(self.db_dir.get(), wxid, output_path, db_version)
                        else:
                            self.log(f"不支持的导出格式: {format_type}")
                            continue

                        exporter.export()
                        self.log(f"✓ {wxid} 导出完成")

                    except Exception as e:
                        self.log(f"✗ {wxid} 导出失败: {e}")

                self.log("所有导出任务完成!")
                messagebox.showinfo("完成", f"导出完成!\n输出目录: {output_path}")

            except Exception as e:
                self.log(f"导出过程出错: {e}")
                self.log(f"错误详情: {traceback.format_exc()}")
                messagebox.showerror("错误", f"导出失败: {e}")
            finally:
                self.progress.stop()
                self.export_btn.config(state="normal")

        threading.Thread(target=export_worker, daemon=True).start()

    def show_about(self):
        """显示关于信息"""
        about_text = """微信聊天记录解密工具 v3.0

功能特点:
• 支持微信3.x和4.0版本
• 自动解密微信数据库
• 多格式导出聊天记录
• 用户友好的图形界面

使用说明:
1. 确保微信已登录
2. 选择对应的微信版本
3. 点击"解密微信数据库"
4. 选择要导出的联系人
5. 选择导出格式并开始导出

注意事项:
• 请在使用前关闭微信
• 首次使用可能需要管理员权限
• 导出大量数据可能需要较长时间

开源地址: https://github.com/LC044/WeChatMsg
"""
        messagebox.showinfo("关于", about_text)


def main():
    """主函数"""
    # 支持多进程
    freeze_support()

    # 创建主窗口
    root = tk.Tk()
    app = WeChatDecryptGUI(root)

    # 设置窗口关闭事件
    def on_closing():
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # 启动GUI
    root.mainloop()


if __name__ == "__main__":
    main()
