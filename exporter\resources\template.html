<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .flatpickr-calendar {
            background: transparent;
            opacity: 0;
            display: none;
            text-align: center;
            visibility: hidden;
            padding: 0;
            -webkit-animation: none;
            animation: none;
            direction: ltr;
            border: 0;
            font-size: 14px;
            line-height: 24px;
            border-radius: 5px;
            position: absolute;
            width: 307.875px;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            -ms-touch-action: manipulation;
            touch-action: manipulation;
            background: #fff;
            -webkit-box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, 0.08);
            box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, 0.08)
        }

        .flatpickr-calendar.open,
        .flatpickr-calendar.inline {
            opacity: 1;
            max-height: 640px;
            visibility: visible
        }

        .flatpickr-calendar.open {
            display: inline-block;
            z-index: 99999
        }

        .flatpickr-calendar.animate.open {
            -webkit-animation: fpFadeInDown 300ms cubic-bezier(.23, 1, .32, 1);
            animation: fpFadeInDown 300ms cubic-bezier(.23, 1, .32, 1)
        }

        .flatpickr-calendar.inline {
            display: block;
            position: relative;
            top: 2px
        }

        .flatpickr-calendar.static {
            position: absolute;
            top: calc(100% + 2px)
        }

        .flatpickr-calendar.static.open {
            z-index: 999;
            display: block
        }

        .flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7) {
            -webkit-box-shadow: none !important;
            box-shadow: none !important
        }

        .flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1) {
            -webkit-box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
            box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6
        }

        .flatpickr-calendar .hasWeeks .dayContainer,
        .flatpickr-calendar .hasTime .dayContainer {
            border-bottom: 0;
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0
        }

        .flatpickr-calendar .hasWeeks .dayContainer {
            border-left: 0
        }

        .flatpickr-calendar.hasTime .flatpickr-time {
            height: 40px;
            border-top: 1px solid #e6e6e6
        }

        .flatpickr-calendar.noCalendar.hasTime .flatpickr-time {
            height: auto
        }

        .flatpickr-calendar:before,
        .flatpickr-calendar:after {
            position: absolute;
            display: block;
            pointer-events: none;
            border: solid transparent;
            content: '';
            height: 0;
            width: 0;
            left: 22px
        }

        .flatpickr-calendar.rightMost:before,
        .flatpickr-calendar.arrowRight:before,
        .flatpickr-calendar.rightMost:after,
        .flatpickr-calendar.arrowRight:after {
            left: auto;
            right: 22px
        }

        .flatpickr-calendar.arrowCenter:before,
        .flatpickr-calendar.arrowCenter:after {
            left: 50%;
            right: 50%
        }

        .flatpickr-calendar:before {
            border-width: 5px;
            margin: 0 -5px
        }

        .flatpickr-calendar:after {
            border-width: 4px;
            margin: 0 -4px
        }

        .flatpickr-calendar.arrowTop:before,
        .flatpickr-calendar.arrowTop:after {
            bottom: 100%
        }

        .flatpickr-calendar.arrowTop:before {
            border-bottom-color: #e6e6e6
        }

        .flatpickr-calendar.arrowTop:after {
            border-bottom-color: #fff
        }

        .flatpickr-calendar.arrowBottom:before,
        .flatpickr-calendar.arrowBottom:after {
            top: 100%
        }

        .flatpickr-calendar.arrowBottom:before {
            border-top-color: #e6e6e6
        }

        .flatpickr-calendar.arrowBottom:after {
            border-top-color: #fff
        }

        .flatpickr-calendar:focus {
            outline: 0
        }

        .flatpickr-wrapper {
            position: relative;
            display: inline-block
        }

        .flatpickr-months {
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex
        }

        .flatpickr-months .flatpickr-month {
            background: transparent;
            color: rgba(0, 0, 0, 0.9);
            fill: rgba(0, 0, 0, 0.9);
            height: 34px;
            line-height: 1;
            text-align: center;
            position: relative;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            overflow: hidden;
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            -ms-flex: 1;
            flex: 1
        }

        .flatpickr-months .flatpickr-prev-month,
        .flatpickr-months .flatpickr-next-month {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            text-decoration: none;
            cursor: pointer;
            position: absolute;
            top: 0;
            height: 34px;
            padding: 10px;
            z-index: 3;
            color: rgba(0, 0, 0, 0.9);
            fill: rgba(0, 0, 0, 0.9)
        }

        .flatpickr-months .flatpickr-prev-month.flatpickr-disabled,
        .flatpickr-months .flatpickr-next-month.flatpickr-disabled {
            display: none
        }

        .flatpickr-months .flatpickr-prev-month i,
        .flatpickr-months .flatpickr-next-month i {
            position: relative
        }

        .flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
        .flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
            /*
      /*rtl:begin:ignore*/
            left: 0
                /*
      /*rtl:end:ignore*/
        }

        /*
      /*rtl:begin:ignore*/
        /*
      /*rtl:end:ignore*/
        .flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
        .flatpickr-months .flatpickr-next-month.flatpickr-next-month {
            /*
      /*rtl:begin:ignore*/
            right: 0
                /*
      /*rtl:end:ignore*/
        }

        /*
      /*rtl:begin:ignore*/
        /*
      /*rtl:end:ignore*/
        .flatpickr-months .flatpickr-prev-month:hover,
        .flatpickr-months .flatpickr-next-month:hover {
            color: #959ea9
        }

        .flatpickr-months .flatpickr-prev-month:hover svg,
        .flatpickr-months .flatpickr-next-month:hover svg {
            fill: #f64747
        }

        .flatpickr-months .flatpickr-prev-month svg,
        .flatpickr-months .flatpickr-next-month svg {
            width: 14px;
            height: 14px
        }

        .flatpickr-months .flatpickr-prev-month svg path,
        .flatpickr-months .flatpickr-next-month svg path {
            -webkit-transition: fill .1s;
            transition: fill .1s;
            fill: inherit
        }

        .numInputWrapper {
            position: relative;
            height: auto
        }

        .numInputWrapper input,
        .numInputWrapper span {
            display: inline-block
        }

        .numInputWrapper input {
            width: 100%
        }

        .numInputWrapper input::-ms-clear {
            display: none
        }

        .numInputWrapper input::-webkit-outer-spin-button,
        .numInputWrapper input::-webkit-inner-spin-button {
            margin: 0;
            -webkit-appearance: none
        }

        .numInputWrapper span {
            position: absolute;
            right: 0;
            width: 14px;
            padding: 0 4px 0 2px;
            height: 50%;
            line-height: 50%;
            opacity: 0;
            cursor: pointer;
            border: 1px solid rgba(57, 57, 57, 0.15);
            -webkit-box-sizing: border-box;
            box-sizing: border-box
        }

        .numInputWrapper span:hover {
            background: rgba(0, 0, 0, 0.1)
        }

        .numInputWrapper span:active {
            background: rgba(0, 0, 0, 0.2)
        }

        .numInputWrapper span:after {
            display: block;
            content: "";
            position: absolute
        }

        .numInputWrapper span.arrowUp {
            top: 0;
            border-bottom: 0
        }

        .numInputWrapper span.arrowUp:after {
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 4px solid rgba(57, 57, 57, 0.6);
            top: 26%
        }

        .numInputWrapper span.arrowDown {
            top: 50%
        }

        .numInputWrapper span.arrowDown:after {
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid rgba(57, 57, 57, 0.6);
            top: 40%
        }

        .numInputWrapper span svg {
            width: inherit;
            height: auto
        }

        .numInputWrapper span svg path {
            fill: rgba(0, 0, 0, 0.5)
        }

        .numInputWrapper:hover {
            background: rgba(0, 0, 0, 0.05)
        }

        .numInputWrapper:hover span {
            opacity: 1
        }

        .flatpickr-current-month {
            font-size: 135%;
            line-height: inherit;
            font-weight: 300;
            color: inherit;
            position: absolute;
            width: 75%;
            left: 12.5%;
            padding: 7.48px 0 0 0;
            line-height: 1;
            height: 34px;
            display: inline-block;
            text-align: center;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0)
        }

        .flatpickr-current-month span.cur-month {
            font-family: inherit;
            font-weight: 700;
            color: inherit;
            display: inline-block;
            margin-left: .5ch;
            padding: 0
        }

        .flatpickr-current-month span.cur-month:hover {
            background: rgba(0, 0, 0, 0.05)
        }

        .flatpickr-current-month .numInputWrapper {
            width: 6ch;
            width: 7ch\0;
            display: inline-block
        }

        .flatpickr-current-month .numInputWrapper span.arrowUp:after {
            border-bottom-color: rgba(0, 0, 0, 0.9)
        }

        .flatpickr-current-month .numInputWrapper span.arrowDown:after {
            border-top-color: rgba(0, 0, 0, 0.9)
        }

        .flatpickr-current-month input.cur-year {
            background: transparent;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            color: inherit;
            cursor: text;
            padding: 0 0 0 .5ch;
            margin: 0;
            display: inline-block;
            font-size: inherit;
            font-family: inherit;
            font-weight: 300;
            line-height: inherit;
            height: auto;
            border: 0;
            border-radius: 0;
            vertical-align: initial;
            -webkit-appearance: textfield;
            -moz-appearance: textfield;
            appearance: textfield
        }

        .flatpickr-current-month input.cur-year:focus {
            outline: 0
        }

        .flatpickr-current-month input.cur-year[disabled],
        .flatpickr-current-month input.cur-year[disabled]:hover {
            font-size: 100%;
            color: rgba(0, 0, 0, 0.5);
            background: transparent;
            pointer-events: none
        }

        .flatpickr-current-month .flatpickr-monthDropdown-months {
            appearance: menulist;
            background: transparent;
            border: none;
            border-radius: 0;
            box-sizing: border-box;
            color: inherit;
            cursor: pointer;
            font-size: inherit;
            font-family: inherit;
            font-weight: 300;
            height: auto;
            line-height: inherit;
            margin: -1px 0 0 0;
            outline: none;
            padding: 0 0 0 .5ch;
            position: relative;
            vertical-align: initial;
            -webkit-box-sizing: border-box;
            -webkit-appearance: menulist;
            -moz-appearance: menulist;
            width: auto
        }

        .flatpickr-current-month .flatpickr-monthDropdown-months:focus,
        .flatpickr-current-month .flatpickr-monthDropdown-months:active {
            outline: none
        }

        .flatpickr-current-month .flatpickr-monthDropdown-months:hover {
            background: rgba(0, 0, 0, 0.05)
        }

        .flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
            background-color: transparent;
            outline: none;
            padding: 0
        }

        .flatpickr-weekdays {
            background: transparent;
            text-align: center;
            overflow: hidden;
            width: 100%;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center;
            height: 28px
        }

        .flatpickr-weekdays .flatpickr-weekdaycontainer {
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            -ms-flex: 1;
            flex: 1
        }

        span.flatpickr-weekday {
            cursor: default;
            font-size: 90%;
            background: transparent;
            color: rgba(0, 0, 0, 0.54);
            line-height: 1;
            margin: 0;
            text-align: center;
            display: block;
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            -ms-flex: 1;
            flex: 1;
            font-weight: bolder
        }

        .dayContainer,
        .flatpickr-weeks {
            padding: 1px 0 0 0
        }

        .flatpickr-days {
            position: relative;
            overflow: hidden;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: start;
            -webkit-align-items: flex-start;
            -ms-flex-align: start;
            align-items: flex-start;
            width: 307.875px
        }

        .flatpickr-days:focus {
            outline: 0
        }

        .dayContainer {
            padding: 0;
            outline: 0;
            text-align: left;
            width: 307.875px;
            min-width: 307.875px;
            max-width: 307.875px;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            display: inline-block;
            display: -ms-flexbox;
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            -webkit-flex-wrap: wrap;
            flex-wrap: wrap;
            -ms-flex-wrap: wrap;
            -ms-flex-pack: justify;
            -webkit-justify-content: space-around;
            justify-content: space-around;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
            opacity: 1
        }

        .dayContainer+.dayContainer {
            -webkit-box-shadow: -1px 0 0 #e6e6e6;
            box-shadow: -1px 0 0 #e6e6e6
        }

        .flatpickr-day {
            background: none;
            border: 1px solid transparent;
            border-radius: 150px;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            color: #393939;
            cursor: pointer;
            font-weight: 400;
            width: 14.2857143%;
            -webkit-flex-basis: 14.2857143%;
            -ms-flex-preferred-size: 14.2857143%;
            flex-basis: 14.2857143%;
            max-width: 39px;
            height: 39px;
            line-height: 39px;
            margin: 0;
            display: inline-block;
            position: relative;
            -webkit-box-pack: center;
            -webkit-justify-content: center;
            -ms-flex-pack: center;
            justify-content: center;
            text-align: center
        }

        .flatpickr-day.inRange,
        .flatpickr-day.prevMonthDay.inRange,
        .flatpickr-day.nextMonthDay.inRange,
        .flatpickr-day.today.inRange,
        .flatpickr-day.prevMonthDay.today.inRange,
        .flatpickr-day.nextMonthDay.today.inRange,
        .flatpickr-day:hover,
        .flatpickr-day.prevMonthDay:hover,
        .flatpickr-day.nextMonthDay:hover,
        .flatpickr-day:focus,
        .flatpickr-day.prevMonthDay:focus,
        .flatpickr-day.nextMonthDay:focus {
            cursor: pointer;
            outline: 0;
            background: #e6e6e6;
            border-color: #e6e6e6
        }

        .flatpickr-day.today {
            border-color: #959ea9
        }

        .flatpickr-day.today:hover,
        .flatpickr-day.today:focus {
            border-color: #959ea9;
            background: #959ea9;
            color: #fff
        }

        .flatpickr-day.selected,
        .flatpickr-day.startRange,
        .flatpickr-day.endRange,
        .flatpickr-day.selected.inRange,
        .flatpickr-day.startRange.inRange,
        .flatpickr-day.endRange.inRange,
        .flatpickr-day.selected:focus,
        .flatpickr-day.startRange:focus,
        .flatpickr-day.endRange:focus,
        .flatpickr-day.selected:hover,
        .flatpickr-day.startRange:hover,
        .flatpickr-day.endRange:hover,
        .flatpickr-day.selected.prevMonthDay,
        .flatpickr-day.startRange.prevMonthDay,
        .flatpickr-day.endRange.prevMonthDay,
        .flatpickr-day.selected.nextMonthDay,
        .flatpickr-day.startRange.nextMonthDay,
        .flatpickr-day.endRange.nextMonthDay {
            background: #569ff7;
            -webkit-box-shadow: none;
            box-shadow: none;
            color: #fff;
            border-color: #569ff7
        }

        .flatpickr-day.selected.startRange,
        .flatpickr-day.startRange.startRange,
        .flatpickr-day.endRange.startRange {
            border-radius: 50px 0 0 50px
        }

        .flatpickr-day.selected.endRange,
        .flatpickr-day.startRange.endRange,
        .flatpickr-day.endRange.endRange {
            border-radius: 0 50px 50px 0
        }

        .flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n+1)),
        .flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n+1)),
        .flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n+1)) {
            -webkit-box-shadow: -10px 0 0 #569ff7;
            box-shadow: -10px 0 0 #569ff7
        }

        .flatpickr-day.selected.startRange.endRange,
        .flatpickr-day.startRange.startRange.endRange,
        .flatpickr-day.endRange.startRange.endRange {
            border-radius: 50px
        }

        .flatpickr-day.inRange {
            border-radius: 0;
            -webkit-box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
            box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6
        }

        .flatpickr-day.flatpickr-disabled,
        .flatpickr-day.flatpickr-disabled:hover,
        .flatpickr-day.prevMonthDay,
        .flatpickr-day.nextMonthDay,
        .flatpickr-day.notAllowed,
        .flatpickr-day.notAllowed.prevMonthDay,
        .flatpickr-day.notAllowed.nextMonthDay {
            color: rgba(57, 57, 57, 0.3);
            background: transparent;
            border-color: transparent;
            cursor: default
        }

        .flatpickr-day.flatpickr-disabled,
        .flatpickr-day.flatpickr-disabled:hover {
            cursor: not-allowed;
            color: rgba(57, 57, 57, 0.1)
        }

        .flatpickr-day.week.selected {
            border-radius: 0;
            -webkit-box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7;
            box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7
        }

        .flatpickr-day.hidden {
            visibility: hidden
        }

        .rangeMode .flatpickr-day {
            margin-top: 1px
        }

        .flatpickr-weekwrapper {
            float: left
        }

        .flatpickr-weekwrapper .flatpickr-weeks {
            padding: 0 12px;
            -webkit-box-shadow: 1px 0 0 #e6e6e6;
            box-shadow: 1px 0 0 #e6e6e6
        }

        .flatpickr-weekwrapper .flatpickr-weekday {
            float: none;
            width: 100%;
            line-height: 28px
        }

        .flatpickr-weekwrapper span.flatpickr-day,
        .flatpickr-weekwrapper span.flatpickr-day:hover {
            display: block;
            width: 100%;
            max-width: none;
            color: rgba(57, 57, 57, 0.3);
            background: transparent;
            cursor: default;
            border: none
        }

        .flatpickr-innerContainer {
            display: block;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            overflow: hidden
        }

        .flatpickr-rContainer {
            display: inline-block;
            padding: 0;
            -webkit-box-sizing: border-box;
            box-sizing: border-box
        }

        .flatpickr-time {
            text-align: center;
            outline: 0;
            display: block;
            height: 0;
            line-height: 40px;
            max-height: 40px;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            overflow: hidden;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex
        }

        .flatpickr-time:after {
            content: "";
            display: table;
            clear: both
        }

        .flatpickr-time .numInputWrapper {
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            -ms-flex: 1;
            flex: 1;
            width: 40%;
            height: 40px;
            float: left
        }

        .flatpickr-time .numInputWrapper span.arrowUp:after {
            border-bottom-color: #393939
        }

        .flatpickr-time .numInputWrapper span.arrowDown:after {
            border-top-color: #393939
        }

        .flatpickr-time.hasSeconds .numInputWrapper {
            width: 26%
        }

        .flatpickr-time.time24hr .numInputWrapper {
            width: 49%
        }

        .flatpickr-time input {
            background: transparent;
            -webkit-box-shadow: none;
            box-shadow: none;
            border: 0;
            border-radius: 0;
            text-align: center;
            margin: 0;
            padding: 0;
            height: inherit;
            line-height: inherit;
            color: #393939;
            font-size: 14px;
            position: relative;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            -webkit-appearance: textfield;
            -moz-appearance: textfield;
            appearance: textfield
        }

        .flatpickr-time input.flatpickr-hour {
            font-weight: bold
        }

        .flatpickr-time input.flatpickr-minute,
        .flatpickr-time input.flatpickr-second {
            font-weight: 400
        }

        .flatpickr-time input:focus {
            outline: 0;
            border: 0
        }

        .flatpickr-time .flatpickr-time-separator,
        .flatpickr-time .flatpickr-am-pm {
            height: inherit;
            float: left;
            line-height: inherit;
            color: #393939;
            font-weight: bold;
            width: 2%;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-align-self: center;
            -ms-flex-item-align: center;
            align-self: center
        }

        .flatpickr-time .flatpickr-am-pm {
            outline: 0;
            width: 18%;
            cursor: pointer;
            text-align: center;
            font-weight: 400
        }

        .flatpickr-time input:hover,
        .flatpickr-time .flatpickr-am-pm:hover,
        .flatpickr-time input:focus,
        .flatpickr-time .flatpickr-am-pm:focus {
            background: #eee
        }

        .flatpickr-input[readonly] {
            cursor: pointer
        }

        @-webkit-keyframes fpFadeInDown {
            from {
                opacity: 0;
                -webkit-transform: translate3d(0, -20px, 0);
                transform: translate3d(0, -20px, 0)
            }

            to {
                opacity: 1;
                -webkit-transform: translate3d(0, 0, 0);
                transform: translate3d(0, 0, 0)
            }
        }

        @keyframes fpFadeInDown {
            from {
                opacity: 0;
                -webkit-transform: translate3d(0, -20px, 0);
                transform: translate3d(0, -20px, 0)
            }

            to {
                opacity: 1;
                -webkit-transform: translate3d(0, 0, 0);
                transform: translate3d(0, 0, 0)
            }
        }
    </style>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        body {
            margin: 0;
            display: flex;
            flex-direction: row;
            height: 100vh;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5
        }

        ::-webkit-scrollbar {
            width: 10px
        }

        ::-webkit-scrollbar-track {
            border-radius: 8px
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: 0 0
        }

        .page {
            display: flex;
            flex-direction: row;
            width: 100%;
            height: 100%;
            max-width: 1200px;
            box-shadow: 1px 1px 3px #ebebeb;
            border-radius: 5px
        }

        .page .side-bar {
            width: 50px;
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px
        }

        .page .mid-bar {
            width: 300px;
            position: relative;
            --left: 60%;
            --color: #0d5dff
        }

        .page .main-body {
            flex: 1;
            display: flex;
            width: 100%;
            flex-direction: column;
            background-color: #f5f5f5;
            border-top-right-radius: 5px;
            border-bottom-right-radius: 5px
        }

        .page .main-body .title-bar {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            padding-top: 3px
        }

        .page .main-body .title-bar p {
            margin: 5px 25px;
            font-size: 18px;
            max-width: 180px;
            align-self: flex-start
        }

        .page .main-body .container {
            flex: 1;
            border-top: solid 3px #f0f0f0;
            border-bottom: solid 3px #f0f0f0
        }

        .page .main-body .nav-bar,
        .page .main-body .nav-bar .turner-bar {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center
        }

        .page .main-body .nav-bar {
            height: 50px
        }

        .page .main-body .nav-bar .turner-bar {
            color: #2e2e2e;
            font-size: 16px;
            user-select: none
        }

        .page .main-body .nav-bar .turner-bar .button {
            display: inline-block;
            background-color: #d6d6d6;
            margin-left: 20px;
            margin-right: 20px;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            transition: all .2s
        }

        .page .main-body .nav-bar .turner-bar .button:hover {
            background-color: #ebebeb
        }

        .page .main-body .nav-bar .turner-bar p {
            display: inline-block;
            margin-left: 5px;
            margin-right: 5px
        }

        .page .main-body .nav-bar .turner-bar input {
            border: 0;
            resize: none;
            outline: 0;
            background: 0 0;
            border-bottom: solid 1px #1f1f1f;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            height: 24px;
            width: 50px
        }

        .page .mid-bar .timeline-area,
        .timeline {
            position: relative;
            height: 100%;
            display: flex;
            overflow-y: auto;
            flex-direction: column
        }

        .page .mid-bar .timeline-area {
            flex: 1;
            padding-right: 5px
        }

        .timeline-wrapper {
            flex: 1;
            overflow-y: auto;
            padding: 30% 0
        }

        .timeline {
            width: 100%;
            overflow: hidden
        }

        .timeline-item-year {
            height: 40px;
            position: relative;
            display: flex
        }

        .timeline-item-month {
            height: 25px;
            display: flex
        }

        .timeline::before {
            content: "";
            position: absolute;
            left: var(--left);
            width: 1px;
            height: 600px;
            top: 20px;
            bottom: 0;
            background-image: linear-gradient(to bottom, rgba(144, 156, 173, .6) 60%, rgba(255, 255, 255, 0) 0%);
            background-position: left;
            background-size: 1px 5px;
            background-repeat: repeat-y
        }

        .timeline-dot-month,
        .timeline-dot-year {
            left: var(--left);
            position: relative;
            border-radius: 50%;
            box-shadow: 0 0 0 1px #d8d8d8;
            text-align: center;
            top: 50%;
            transform: translateY(-50%);
            line-height: 40px
        }

        .timeline-dot-month {
            background: #fff;
            width: 7px;
            height: 7px;
            margin-left: -3px
        }

        .timeline-dot-year {
            width: 10px;
            height: 10px;
            background: #000;
            margin-left: -4.5px
        }

        .timeline-item-month.current .timeline-dot-month {
            width: 7px;
            height: 7px;
            background-color: var(--color);
            box-shadow: 0 0 4px var(--color);
            border: 1px solid #fff;
            margin-left: -3px
        }

        .timeline-item-month .timeline-right,
        .timeline-item-year .timeline-right {
            position: relative;
            margin: 0 0 0 calc(var(--left) + 15px);
            height: 40px;
            line-height: 40px
        }

        .timeline-item-month .timeline-right {
            height: 25px;
            line-height: 25px
        }

        .timeline-right:hover {
            color: red
        }

        .no-msg-month {
            color: #9e9898
        }

        .hidden-month {
            display: none
        }

        .container {
            display: flex;
            overflow: hidden;
            justify-content: center
        }

        .container .content {
            width: calc(100% - 40px);
            padding: 20px;
            overflow-y: scroll;
            flex: 1
        }

        .container .content:hover::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, .1)
        }

        .container .content:hover::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, .3)
        }

        @media screen and (max-width:768px) {
            .container .content {
                width: calc(100% - 40px);
                padding: 10px;
                overflow-y: scroll;
                flex: 1
            }
        }

        .bubble,
        .chat-refer {
            max-width: 400px;
            position: relative;
            word-wrap: break-word;
            word-break: normal
        }

        .bubble {
            white-space: pre-wrap;
            padding: 10px 11px;
            border-radius: 5px;
            color: #1f1f1f;
            font-size: 14px
        }

        .chat-refer {
            margin-top: 5px;
            padding: 8px 10px;
            border-radius: 3px;
            color: #797979;
            font-size: 12px;
            background-color: #e8e8e8;
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2; /* 这里添加 */
            line-clamp: 2;        /* 这里添加 */
            white-space: normal;   /* 这里添加 */
        }

        .chat-refer-right {
            margin-right: 15px
        }

        .chat-refer-left {
            margin-left: 15px
        }

        .item-left .bubble {
            margin-left: 15px;
            background-color: #fff
        }

        .item-left .bubble:before,
        .item-right .bubble:before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent
        }

        .item-left .bubble:before {
            left: -18px;
            border-left: 10px solid transparent;
            border-right: 10px solid #fff
        }

        .item-right .bubble {
            margin-right: 15px;
            background-color: #9eea6a
        }

        .item-right .bubble:before {
            border-left: 10px solid #9eea6a;
            border-right: 10px solid transparent;
            right: -18px
        }

        @media screen and (max-width: 768px) {
            .bubble {
                max-width: 280px;
            }

            .item-right .bubble {
                margin-right: 8px;
            }

            .item-left .bubble {
                margin-left: 8px;
            }

            .item-left .bubble:before {
                content: "";
                position: absolute;
                width: 0;
                height: 0;
                border-left: 8px solid transparent;
                border-top: 8px solid transparent;
                border-right: 8px solid #fff;
                border-bottom: 8px solid transparent;
                left: -13px;
            }

            .item-right .bubble:before {
                content: "";
                position: absolute;
                width: 0;
                height: 0;
                border-left: 8px solid #9eea6a;
                border-top: 8px solid transparent;
                border-right: 8px solid transparent;
                border-bottom: 8px solid transparent;
                right: -13px;
            }
        }

        .item {
            margin-top: 15px;
            display: flex;
            width: 100%;
        }

        .item-refer {
            margin-top: 4px;
        }

        .item.item-right {
            justify-content: flex-end;
        }

        .item.item-center {
            justify-content: center;
        }

        .item.item-center span {
            font-size: 12px;
            padding: 2px 4px;
            color: #fff;
            background-color: #dadada;
            border-radius: 3px;
            -moz-user-select: none;
            /*火狐*/
            -webkit-user-select: none;
            /*webkit浏览器*/
            -ms-user-select: none;
            /*IE10*/
            -khtml-user-select: none;
            /*早期浏览器*/
            user-select: none;
        }

        .content-wrapper {
            display: flex;
            flex-direction: column;
            max-width: 50%;
        }

        .content-wrapper-left {
            align-items: baseline;
        }

        .content-wrapper-right {
            align-items: flex-end;
        }

        .displayname {
            margin-left: 13px;
            margin-left: 13px;
            font-size: 13px;
            margin-bottom: 5px;
            color: darkgray;
        }
        .avatar img {
            width: 42px;
            height: 42px;
            border-radius: 3px;
            user-select: none;
        }

        .chat-video video {
            margin-right: 18px;
            margin-left: 18px;
            max-width: 350px;
        }

        .chat-audio {
            max-width: 400px;
        }

        audio {
            margin-left: 9px;
            margin-right: 9px;
        }
        .chat-image img,
        .chat-file img {
            margin-right: 18px;
            margin-left: 18px;
            max-width: 250px;
            max-height: 250px;
        }
        .emoji-image img {
            margin-right: 18px;
            margin-left: 18px;
            max-width: 150px;
            max-height: 150px;
        }

        @media screen and (max-width: 768px) {

            .chat-image img,
            .chat-file img {
                margin-right: 8px;
                margin-left: 8px;
                max-width: 250px;
                /*max-height: auto;*/
            }
            .displayname {
            margin-left: 13px;
            margin-left: 13px;
            font-size: 10px;
            margin-bottom: 5px;
            color: darkgray;
            }
        }

        .chat-file {
            max-width: 80%;
            width: 300px;
            margin-right: 20px;
            display: flex;
            flex-direction: column;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            cursor: pointer;
            /* height: 100px; */
            margin-left: 10px;
        }

        .chat-file img {
            width: 50px;
        }

        @media screen and (max-width: 768px) {
            .chat-file {
                margin-left: 8px;
                margin-right: 8px;
            }
            .chat-file img {
                width: 40px;
            }
            .chat-image img,
            .chat-file img {
                margin-right: 8px;
                margin-left: 8px;
            }
        }

        .chat-music-audio {
            margin: 0 10px;
        }

        .chat-music-audio .player-box,
        .chat-file .file-box {
            display: flex;
            align-items: center;
            cursor: pointer;
            /* height: 80px; */
        }

        .player-box {
            background-color: #939AA1;
            border-radius: 5px;
            width: 300px;
        }

        .player-box .cover-image {
            width: 80px;
            height: 80px;
            border-radius: 5px 0 0 5px;
            vertical-align: middle
        }

        .player-box .player-info {
            width: 160px;
            margin: 0 14px;
            overflow: hidden;
            vertical-align: middle
        }

        .player-box .player-info .title {
            font-size: 16px;
            color: #fff;
            margin-bottom: 5px;
        }

        .player-box .player-info .artist {
            font-size: 14px;
            color: #cecece;
        }

        .player-box .player-button {
            width: 16px;
            height: 16px;
            vertical-align: middle
        }

        .player-original {
            display: flex;
            align-items: center;
            height: 20px;
        }

        .player-original p {
            color: #b6b6b6;
            font-size: 12px;
            vertical-align: middle
        }

        .player-original-img {
            width: 12px;
            height: 12px;
            margin: 0 5px;
        }

        .chat-file .app-info {
            border-top: 1px solid #ede3e3;
        }

        .chat-file .app-info p {
            margin-top: 3px;
            color: #888;
        }

        .chat-music-audio .player-controls,
        .chat-file .file-img {
            display: flex;
            align-items: center;
        }

        .file-title{
            font-size: 14px;
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
        }

        .description>p {
            font-size: 12px;
            color: #BDBDBD;
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
        }

        .card-title{
            font-size: 14px;
        }

        .file-title,
        .card-content>h2 {
            -webkit-line-clamp: 2;
            font: 14px sans-serif;
        }

        @media screen and (max-width: 768px) {
            .file-title {
                font-size: 13px;
            }
        }

        .description>p {
            -webkit-line-clamp: 3;
            margin-top: 2px;
        }


        .chat-music-audio .flex1,
        .chat-file .flex1 {
            flex: 1;
            justify-content: start;
        }

        .chat-music-audio .flex2,
        .chat-file .flex2 {
            flex: 2;
            justify-content: end;
        }

        .chat-file .file-info {
            width: 200px;
            /* height: 80px; */
            margin-left: 4px;
            /* margin-top: 15px; */
            white-space: normal;
            flex-basis: 200px;
            word-break: break-word;
        }

        .chat-file .file-name,
        .chat-music-audio .song-title {
            font-weight: 700;
            overflow-wrap: break-word
        }

        .chat-file .file-size {
            margin-top: 5px;
            color: #888
        }

        .app-info {
            font-size: 13px;
            margin-left: 3px;
            margin-right: 3px
        }

        .file-size,
        textarea {
            font-size: 12px
        }

        .chat-music-audio .play-button {
            width: 50px;
            height: 50px;
            background-color: transparent;
            border-radius: 50%;
            border: 0;
            outline: 0;
            cursor: pointer
        }

        .chat-music-audio .play-button.playing {
            background: url(data:image/png;base64,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) no-repeat center;
            background-size: 18px 18px;
        }

        .chat-music-audio .play-button.paused {
            background: url(data:image/png;base64,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) no-repeat center;
            background-size: 18px 18px;
        }

        .input-area {
            border-top: .5px solid #e0e0e0;
            height: 150px;
            display: flex;
            flex-flow: column;
            background-color: #fff
        }

        textarea {
            flex: 1;
            padding: 5px;
            border: 0;
            cursor: pointer;
            overflow-y: auto;
            overflow-x: hidden;
            outline: 0;
            resize: none
        }

        .button-area {
            display: flex;
            height: 40px;
            margin-right: 10px;
            line-height: 40px;
            padding: 5px;
            justify-content: flex-end
        }

        .button-area button,
        button {
            border: 0;
            border-radius: 4px;
            cursor: pointer
        }

        .button-area button {
            width: 80px;
            outline: 0;
            float: right
        }

        #paginationInfo,
        .button-row,
        .jump-row {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px
        }

        button,
        input {
            font-size: 14px
        }

        button {
            background-color: #3498db;
            color: #fff;
            margin: 0 14px;
            transition: background-color .3s;
            padding: 10px 25px
        }

        button:hover {
            background-color: #2980b9
        }

        input {
            padding: 8px;
            width: 120px;
            box-sizing: border-box;
            margin-right: 0;
            margin-left: 15px
        }

        #paginationInfo {
            color: #555;
            font-size: 14px
        }

        .card {
            background-color: #fff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, .1);
            max-width: 80%;
            width: 280px;
            display: flex;
            flex-direction: column;
            text-align: left;
            margin: 0 15px 10px 20px
        }

        @media screen and (max-width:768px) {
            .card {
                margin: 0 8px
            }
        }

        .card a {
            text-decoration: none;
            color: inherit
        }

        .card-content {
            padding: 10px;
            flex: 1
        }

        .thumbnail {
            width: 50px;
            height: 50px;
            object-fit: cover
        }

        .description,
        .link-info {
            display: flex;
            justify-content: space-between
        }

        .link-info {
            align-items: center;
            justify-content: center;
            padding: 2px;
            background-color: #f0f0f0
        }

        .app-logo {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 50%
        }

        .app-name {
            font-size: 12px;
            font-weight: bold;
        }

        .transfer-box {
            width: 200px;
            height: 80px;
            margin-left: 18px;
            margin-right: 18px;
            border-radius: 3px;
            padding-left: 11px;
            padding-right: 11px;
            position: relative
        }

        .transfer-box[paysubtype="1"],
        .transfer-box[paysubtype="1"]::before {
            background: #f79c46
        }

        .transfer-box[paysubtype="1"] i {
            background-image: url(data:image/png;base64,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)
        }

        .transfer-box:not([paysubtype="1"]),
        .transfer-box:not([paysubtype="1"])::before {
            background: #f8e2c6
        }

        .transfer-box[paysubtype="3"] i {
            background-image: url(data:image/png;base64,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)
        }

        .transfer-box[paysubtype="4"] i {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADkAAAA4CAYAAABHRFAgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAAULSURBVGhD3ZvZUxtHEId7FnZXHAapApg4wQG/5K6kkqqk8pBUkv88KVdu5/CDyzmqArZsY0MAIyO0K6RJd09L1sne2l2+KkA7oKV/TO9MH4NqPfhOQ4o4ag5UbQdg8RUAZcloCHQX4PwI9PG/4HcvZDAdUhHpuCugrr0KUFnFO87hCN7SOwV9+Fdog92Nd/DTCr5S+PaOeX/jKfitY/MDCUgkksVtvIuv8BYXLZyJE/BOds03E+BWt/EPVgWwK3ilQB/cTyQ2tkh380NjxPkxeId/ymj6uGtvAizUADo+eI9/ldFoRBbp1m4BLK3hO+dA138AX8d2hNA46PLqjc+NGzePwDv6R74Tjkgi3RsfAVg2wIunqbhlVNiNl6+zWO/RLzIaTOjlz73xMf70PHR3v8lFIEG/16v/iFNjGXtCEijSwQ/39U/4tVf/CdrztBjkC9lBkF1OCHsCRSoSiM+E9/iOjBQDtufCB7X5gYxM51KR7BJd9P+Yq1rWePu/8wQEue5UkWaRmSvcDI7C9qGdlwmdKJK3CVxFu7vfykixYTtxUXTXMWqawJhIu3Vq9kHcJoqwyISB7NTPH+DsXJORYcZEWttf8Eaf1zYRF7/xxGwtFImNMCSSYlEK1SiSKSN67zbb71QwDBxgSCQH2xiLziJUywIfFyCyX62/JSOGvkieRcwmuvWfzUBJMcmCBofCP6EvkvNBTJfaFRJbcvwzUEsbcjHorpTwYj54FdC4M4C9IFcikkoWZVxRp+GfPWM9rAthkVyToew+A+z2ubyaNVp09dyVik4eBgEp465ugXXra7maMRTUkC7EiMRNlIpOaeFceLgpY3aw8pqMzB59cI91EeYzklYZ0K1tg9r5Ev10Ea+UGcwBX77a7SZYo9FBElwKJpZxKyoQavUmWGqhKpfxcRbXTPWAA4pioSpVUNprUHgA3sPvZTgapjT5ck/KFHQ9b/8PuQjG3foM/fYFPpPzrgxFh28yK4EEP+cRwTTMAlwJS0OcPbfjobs29jXVMuO6q4MxoqrexHXalpFx4t47KexpZwdgaUxNkkAhFBV6NVe1i5ei6dYJiny+J5fJ8PEv5j3EZJsaPwXCbx6C1ZZl31HpbNzek9+4C0XPQp4z61C/UzARDw6o9bf5ZRpQm41qtTyzOaE23mNdhBF5fpTZRp7XogOU/JMuhEVSCzvPODMblOgSkRyc645pjV0BaFsjPb2ko5+FcD5JLewrgFpaHwoc+iJNXaQcFfNAqJIuzyPRF+lzUKBMj77EGPsVOuYjM4C8dFeEV8KFGvfoSwvaP7qiD4lkOr45hFBCOFZF+0cZE8kN1xKutA4Vx3Hzn9QwHp9JookP7UCZvQyo1S1cWBpyNcxEkXxOhmZTDkQUHbaTZvHZPRkZJvAcD7Wpi9xS77XRL7NxsrsOQv34CY3NIuBefx/tmw+chECRev8uUB0o6ITFrGF77EVzeCmAQJE+JsG9GxXlGe3ZEUYgEeyuArtElxajT81ynQP0e/uLTIR1IvopSTpGQrEh9U/2bpsWdsbY6E3WzlcsjraJaavoNGKdd6VjMHxKRM67Ugs+qw51LuddB6E+ijmEgLfwzziT4QZoQrjMSekSn8vB5Pe/v7kgFZdEIns4y5vGKKqmc3dXR67v8GPA3iBn0CkfxHRpMJuISyoiB7my/01wGdQfpPYZdZd4pukoG5Ur/SYXfvVpPePjbQD/A1/vC7XhnXAdAAAAAElFTkSuQmCC)
        }

        .transfer-box::before {
            content: "";
            width: 8px;
            height: 8px;
            position: absolute;
            transform: rotate(45deg);
            top: 18px
        }

        .transfer-box[issend="0"]::before {
            left: -3px
        }

        .transfer-box[issend="1"]::before {
            left: 218px
        }

        .trans-content {
            display: flex;
            height: 60px;
            align-items: center;
            position: relative
        }

        .trans-content>i {
            display: block;
            width: 36px;
            height: 36px;
            background-repeat: no-repeat;
            background-size: cover
        }

        .transfer-texts {
            margin-left: 7px;
            display: flex;
            align-items: flex-start;
            flex-direction: column
        }

        .trans-bottom>span,
        .transfer-texts>font,
        .transfer-texts>span {
            color: #fff;
            font-weight: 500
        }

        .transfer-texts>span {
            font-size: 15px
        }

        .transfer-texts>font {
            font-size: 12px;
            padding-left: 2px
        }

        .transfer {
            height: 60px
        }

        .trans-bottom {
            height: 16px;
            margin-top: 1px;
            display: flex;
            align-items: center
        }

        .trans-bottom>span {
            font-size: 10px
        }

        .call i {
            display: inline-block;
            background-repeat: no-repeat;
            background-size: cover;
            vertical-align: middle;
            margin-right: 4px;
            margin-top: -2px
        }

        .call[calltype='1'] i {
            background-image: url(data:image/png;base64,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)
        }

        .call[calltype='0'] i {
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAE7mlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgOS4xLWMwMDIgNzkuZjM1NGVmYywgMjAyMy8xMS8wOS0xMjo0MDoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iIHhtbG5zOnBob3Rvc2hvcD0iaHR0cDovL25zLmFkb2JlLmNvbS9waG90b3Nob3AvMS4wLyIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0RXZ0PSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VFdmVudCMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIDI1LjQgKFdpbmRvd3MpIiB4bXA6Q3JlYXRlRGF0ZT0iMjAyNC0wMi0wM1QwMDo1OTo0OCswODowMCIgeG1wOk1vZGlmeURhdGU9IjIwMjQtMDItMDNUMDE6MDE6MDErMDg6MDAiIHhtcDpNZXRhZGF0YURhdGU9IjIwMjQtMDItMDNUMDE6MDE6MDErMDg6MDAiIGRjOmZvcm1hdD0iaW1hZ2UvcG5nIiBwaG90b3Nob3A6Q29sb3JNb2RlPSIzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOmViMGNiNGVkLTQ0ZGMtOTQ0OC1iZDdkLTUxNzJmMTQzOTc5ZSIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDplYjBjYjRlZC00NGRjLTk0NDgtYmQ3ZC01MTcyZjE0Mzk3OWUiIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDplYjBjYjRlZC00NGRjLTk0NDgtYmQ3ZC01MTcyZjE0Mzk3OWUiPiA8eG1wTU06SGlzdG9yeT4gPHJkZjpTZXE+IDxyZGY6bGkgc3RFdnQ6YWN0aW9uPSJjcmVhdGVkIiBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOmViMGNiNGVkLTQ0ZGMtOTQ0OC1iZDdkLTUxNzJmMTQzOTc5ZSIgc3RFdnQ6d2hlbj0iMjAyNC0wMi0wM1QwMDo1OTo0OCswODowMCIgc3RFdnQ6c29mdHdhcmVBZ2VudD0iQWRvYmUgUGhvdG9zaG9wIDI1LjQgKFdpbmRvd3MpIi8+IDwvcmRmOlNlcT4gPC94bXBNTTpIaXN0b3J5PiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PgboeeUAAB/DSURBVHic7d172Lb5XOj/98PsjLE3ZER2ISES0VpRPxZKtISQKGWTbTr8WCFRUlIpK9n9kGyTbCKxRCtaIZGRTbSyyS6MXXYzo5l51h/fx2+2a+aZmeu+zuu6vq/XcdzH8/DHeX/uuZ/7Ot/3eZ3n97tv//79AQBzucDSAwAA6ycAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJnTIOj7Jvn371vFpDsZh1dWqK1eXrY6prnTg49LVUdWFqiMO/P2wJYYE1mZ/9fUDH8cf+Phy9cnqY9VHq08f+N8fOvAnrMX+/fv39Pj79voT1OIBcNnqRtV3Hvi4bvUd1aFLDgVsleOq91bvq95fHVu9dcmB2H0C4Lz7T9Xtqu+rrl1dfIkhgJ30kerd1d9Vrz3wd1gpAXDuHFXdv7p1ddXqiuv6xMC0/qV6V/VH1WsWnoUdIgAOzoWrB1Y/1Tjxe+8eWLfPVe+onla9auFZ2AEC4OwdUT248Vv/5XLiB5b3lcYVgcdUf73sKGwzAXDWDmvc2PfEA39ecNWfAOB8OqF6XvW46hMLz8IWEgBndvnqEdXP5cQPbL5PVL9U/Wn1tYVnYYsIgNO7efW71XVWdUCANXlW9fjG2gJwjgTAcJHG+/y/XB15vgcCWMbbq0dVb1h6EDafABgr9D25+onVTAOwqC9Uj66euvQgbLa9Pj9v+l4AV6hekJM/sDsuWT2lcUUTFrPJVwCuUf1hdZPVTgOwEfZXT6oeXp2y8CxsoFnfArhu9fwDfwLsqv2NhYMeXJ288CxsmBnfArhS4zd/J39g1+1rPNL8m0sPwnw2LQCObqyn/d1LDwKwJheoHlI9shEEsBab9BbAhaqXVLfd22kANtL+6n7VMw/8ncnNcg/AodVvVA/d82HO2f7Ge3EnpcZhFoe0GSuLfrG6ffWmpQdhebMEwN2qpze2812nk6qPV59tPJv7ieq46qvV19u8t0iA1dvXeO25SGOp8ctUl6qOOfD3dXtbdYfqUwt8bjbIDAHwndXLq6vv+SCneteBj2MbK3O9v7GDF0CNELh+dcPqeo37kr51jZ//WdV983jg1Pb8/Lx///49/zgbF65e2bjsvtcfJzc247h39e0r+w8I7LoLVjdrLNzzztbzerW/+sl1fHFsrj0/Ny8cAD/Ren6QXlLdrnGJD+C8ukbjmf33tfevWx9tLIXOpHY5AI6pPtLe/gC9v7prdfFVf2OAqV29ekL1H+3ta9jvr+sLYvPsagAcWj2xvf3BeVZ1lb35tgB0WHXL9vZqwJcabz8woV0NgGtVJ7Q3PzBfrR7UWFcAYK9drnEj815FwP9Y35fCJtnVAPjmQher/vhM474Cz+8D63RI9YzGo8Wrfl37fPXD6/tS2BS7GABXaTxyt+ofkk9WP7Kn3w2As/d7jSeOVv369opGZDCRXQyApzeebV11Id95b78VAAfl6e3N1c1brvOLYHm7FgCXaqy6t8ofjJOre+39twLgoBze3qxv8pw1fg1sgL0+N697qdu7Vhdb8TGf0rjjH2ATnFjdv3rvio97ozzZxCqt+QrAX7XaIn5bddE1/GcCOLd+qPFU0qpe705sLA/M7rtsdaNdegvgutWnW20AfP96vhcA59ohjZsCV/ma98LqyDV+DazfXRp71LxnlwLgMa32EZknZ7c+YLN9R/WeVve69+Hqe9b6FbAu/0/1F40tofdXJ+5SALym1f0QfL665jq+IwDnw77q4a3ute+U6u5r/QrYa9epXto4r532e/3ve31uXtdv0N9WXXGFx3t29aEVHg9gL+yvXlW9e0XH29fYQt1iZ9vtAo3z4h9Uf1PdsbrkEkOsw/c0Nv9ZheOrlzU24QDYdB+oXt+IgVW4QfUtKzoW63e56pHVsY2nRS621CDrCoDvbnV18+fVP6/oWADr8NrGTdCrcL3q8is6FutzTHXP6s3V49qAXWrXtbTklVd4rL9s3CQBsC3e0lgX4HIrONalq8us4Disx6WrWzQ2qfu+hWc5nXUFwNErOs5nG3fUAmyTE6t/qG7eaq68ugKw+Q6vblvd48CfG2fbAuDY3PwHbKe3VV9qNW+HXrnx+n3SCo7F6t22sTPtj7fBj6tvWwD8U3Xcio4FsE5vrz7XagLgqtVRjaBgc9y0sTfNbVrgrv5za10BcIkVHefjKzoOwLp9qrEV+ipcobEi4JdWdDzOn2tUD2vs2HiFhWc5aOsKgAut4BgnNn6AALbVpxqP8Z1fF60OW8FxOH8uXv1C4+7+rTnxf9O6AmAVvtS4fAawrT7WWA/g/C7kc2R1wfM/DufRkdVPNX7rv0LbdS79/23T0CdUX196CIDz4cuN5XzP78n7qLbr9XtXXKRxmf9XG8vRb+wNfgdjm/4BndiIAIBt9dVWEwAXbrtev7fdxasbVo9uh3ah3aZ/QCflkRdgux3fCIDz6/C2/LfPLXGh6iaNRXz+67KjrN42BQDAtrOJz/a4aXW36t7t6PdNAADAqa7duKv/Ho1lfHeWAACAsVnP/RqX+q+97CjrIQAAmNkR1UOqO7aaNRq2hgAAYFY/V92nuk4Tng+n+4IBmN6PVo9sXOo/cuFZFiMAAJjBYY1n+R/TuMP/8GXHWZ4AAGCXHdLYrOe/NbbotYTyAQIAgF119eou1cMbqydyGgIAgF1zjep2jbv7j1l2lM0lAADYFd9a3b762eq7Fp5l4wkAALbdhau7V3dt3ODHQRAAAGyzu1U/Vd08GySdKwIAgG10q8Z7/N9XXXTZUbaTAABgm3xXpz7Lf6mFZzmjTzfWG7jk0oMcDJdLANh0+6rLV/+9elPjRr9NOfnvrz7VuPHwTgf+vhVcAQBgU+2rjm6cXB/a5pz0v+nz1XOrJ1Sfa+wpsDW/WAsAADbR5aofqX6puuLCs5zRcdXrql+vPnCa/39rTv4lAADYLJetfqB6WJu3Pe+Xqr+unlK9cdFJVkAAALAJjqpu3bjcf+uFZzmj/6heXz2v+pOFZ1kZAQDA0m5Z3atxE92meV31x9ULq5MWnmWlBAAAS7lxdZ/qNtVlFp7ljN5aPad6ZeMGv50jAABYt6s23uO/VXWlZUc5k49VT6xeU3102VH2lgAAYF0uUT2iukN1lYVnOaOvVr9dvaj63wvPshYCAIC9dmR1/+oBjUf6NulxuW9Uz2+c/P+lHXuf/+wIAAD2ylHVj1aPblz236RzzvHVG6pfqf6xcaf/VDbpmwHAbjiq+v7Gif8mC89yRl+t3tFYxOcvF55lUQIAgFU5vLF4z89Vd194ljM6oXpX9dTqBQvPshEEAACrcOPqLtWD2qz3+Kv+vvE+/9Oa6D3+cyIAADg/blDdsbp3m7dZz3urlzVO/J9ZeJaNIwAAOC+uWv10defq25cd5Uz+rbFL34ur9yw7yuYSAACcG5doPNJ3x+p6y45yJidUz2ic+P9u4Vk2ngAA4GBcoLpvY83+67V57/M/r3Gp/x8az/ZzDgQAAOfkltVjGif+I5cd5UxeXz2uOrbxiB8HSQAAcFYu2Djh/1rjmf4LLzrN6Z1cvbuxzsCbqq8tO852EgAAnNZh1dWqX6zu1mZd6j+psUHPb1Z/1ISr962SAACgal/jbv57Vr/QWNRnk3ykU9fs/8rCs+wEAQDANRpr9v+/1dELz3JGH61eVf336kPLjrJbBADAvK5Y3a66X3WthWc5o89Wf1Y9p3rbwrPsJAEAMJ8jqp9oLOTz/cuOciYnVi+pXti4w589IgAA5nLnxkY9P9Rm3eBX48T/guovqlMWnmXnCQCAOdyi+vnGb/wXW3iWM3pT9aQDf/77wrNMQwAA7Lbvqh5V3ay6zMKznNEHq8c3LvXbrGfNBADAbrpCY6Gc21eXXniWM/p09XuNDXuc+BciAAB2x77qso27+h/Y2Lhn36ITnd4XGwv4PLFx4vc+/4IEAMBuOLq6Q/VL1eUXnuWMjqv+R/Ur1b8sPAsHCACA7bOvU5fBvXT1A9VDqxsvNdD/xXHV/6qeUL194Vk4AwEAsH1Oqi5V/ZfGpf7bLTvOmZxQvbF6RvXqhWfh/0IAAGyfk6vHVj/Y2LVvk7yxsYjPc6v9y47C2REAANvniMZz/ZvkXdWzq5c2lvFlwwkAgO2zSXf2/2v15Oo11T8vPAvnggAA4Lz4cvU71Z9UH1h4Fs4DAQDAufGNxs19v199pHFDIltIAABwML5RvbJ6XONS/zcWnYbzTQAAcHa+Vh3b2E/gzbmzf2cIAADOyonVexpr9r9w2VHYCwIAYH225bfnd1Uvqp7SWNSHHSQAANbnAksPcA6OrV5ePav6t2VHYa8JAID1uVJ16NJDnIWPVc+vXpBH+qYhAADW5zJt1lWArzYe6fuTbNYzHQEAsD6b9Mz8cxqX+t/W9tybwAoJAIC5vK76zeodjSsATEoAAMzhXdWvV69vLOPL5AQAwO46qfpo9aTG9rzHLzkMm0UAAOymjzbe439STvycBQEAsFs+XP159RvVpxeehQ0mAAB2w6eqVzd26XvfwrOwBQQAwHb7evWKxnv8b1h2FLaJAADYXq9qPM//6uqUhWdhywgAgO30840Nez639CBsp01akhKAg/eGnPw5HwQAwHa68NIDsN0EAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAwIQEAABMSAAAbKevLT0A200AAGynW1SXXnoItpcAANhOT66eXf1oXss5D/yjAdhet6teVD2vcUUADpoAANhuR1Z3q/6oenr1ncuOw7YQAAC74ZjqvtWrGm8PfMuy47DpBADAbrlK9eDqrdWjqgstOw6bSgAA7KYrVY+t/rG6X0KAMxAAALvrkOpq1VOrt1R3rC666ERsDAEAMIfrVS+tXlL9QHXUksOwPAEAMJdbV/+zcaPgTap9y47DUgQAwPocsvQAp/Ez1eur36putPAsLEAAAKzPZ6tTlh7iNI6qHtp4a+Dx1TWXHYd1EgAA6/PR6j+WHuIsXLF6ZPXi6tHV5ZYdh3UQAADrs0m//Z+V61W/Wr2melh1xKLTsKcEAMD6bMsNd9evfq36m8Yyw+wgAQDAWTms+p7qGdXbqv/S9gQMB0EAAHB2Llx9b/Xn1R9X127EAVtOAABwMA6rfrx6Z/Xb1dXbrMcaOZcEAADnxmHVg6q/bzw54NHBLSUAADgvLlr9SvW66hcaVwTYIgIAYPvsX3qA0/i26kmN+wMeUF1m2XE4WAIAYPucUL2hOnnpQU7j+tVTqhdV96kOXXYczokAANg+F6weW/1Q9aplRzmTmzdC4FXVbReehbMhAAC2zyHV56u/rH62ulPjWf1NcWhj18FnVy/PZkMbSQAAbJ/9nXqJ/XPVn1a3a8TAvy411Fk4urp9Yw2BF1RXW3YcTksAAOyG46o/bCza86vVF9qcmwWPbiwp/PbqdxubDTn/LMw3AGB37K8+Uz2msbHPsxpXCDbFJaqHVO9qbDZ02UWnmZwAANhNH2/cjX+L6qXVZ5cd53QuWz2helN192w/vAgBALDb3t1Ywvdujffiv7ToNKd3jep5jRsFf6y62LLjzEUAAMzhDY3H8u5bvabNWkPgxtXLGjsP/kjOTWvhPzLAXP6kumN1r+qvFp7ljO7ceKLhD6tbLjzLzhMAAPM5oXpu4/33B1bvX3Sa0zu8ukf1/OqZjasD7AEBADCvT1V/0Hj//b81HiXcFJep7l29uHpyddVlx9k9AgCAD1a/Vf3nxt35Jy47zulcqXpwY9XDX6kusug0O0QAAFBjDYF/bqwh8N2NS/CnLDrR6V25emTjqYYHNt4q4HwQAACc1jca9wTcs7GG/2urry060akOaYTA71VvqX64uvCSA20zAQDAWTm5emfjJPtj1d+2OSFwwcZVitc0Hh+8aXXUohNtIQEAwDl5feP+gJ+v/qHNemvgVo0VBf+g8cTAYcuOsz0EAAAH69mNpYV/uTp22VHO5B7V/6ye2NgQiXMgAAA4N75YPb6xmNCvVf972XFO54jGVYpXVL9eXWfZcTabAADgvPhQ9ejqro1HBz+/7Dinc7nqEdWLqsdV37rsOJtJAABwfryzcbL9kcaCPZu0x8C1q1+qXlk9qPEUAQcIAABW4W2N1QRv2lhDYJPcoHFvwJurn1x4lo0hAABYlRMbz+ffv3Gz4JuWHed0jqhuUj2t+uvGfFMTAACs2lerN1a3afzG/cHqpEUnOtVR1c2qVx34uEF16KITLUQAALBXvla9sLFozyOqj7Y5awhcqLpt44rF06prNtk9AgIAgL329eq3GyHwO9WHlx3ndA6rfrb6++pR1TWWHWd9BAAA6/LF6uHVLatnNK4IbIqjqsc2FhP6+cYuhDtNAACwbh+qfq6xhsBzqs8uO87pXK6x2dCLqntVRy86zR4SAAAs5W2Ny+93b5xwN+X+gBpPDPx/jUcaf6odvD9AAACwtNc3QuBO1esWnuWMbtUIgVdWP77sKKslAADYBCdUL69+urpLY4XBTXFo45HGZzT2Gbj5suOshgAAYJN8pnpJ4xG9+1QfW3ac07l49V+rF1fPazw6uLUEAACb6N+qZ1U3rB7ZZm02dHTjvoW/rZ5SXfbA/79J9zCcIwEAwKba33hC4AnVd1W/X/37ohOd3iWrB1Tvqn6mulhbFAECAIBNt7/6ZPXgxjK+L6+OW3Si07tc9ezqpdUxC89y0AQAANvk3dUdGk8M/EX15WXHOZ1vaVwV2AoCAIBt9KbGnfn3r/6yLbr0vikEAADb7IXV7Rvvxb954Vm2igAAYNt9rXp6dbfGfQLvXnac7SAAANgVn2g8KXDnxqZDn1p2nM0mAADYNR+sfqv6weox1VeXHWczCQAAdtU/V79e3bixqc/Jy46zWQQAALvspOp9ja19b9Z4YuDERSfaEAIAgBl8o7F07y0b9wi8vfr6ohMtTAAAMJs/q763emhjGd+Tlh1nGQIAgFk9vfq+6tFt1vbDayEAAJjZCY3Nhm5XPa5677LjrI8AAICxZsAvV3etfrP69LLj7D0BAACnem/1i9Udq2c2diLcSQIAYH129mSyg/62ekh1i+qVi06yR7YpAA458AGwrS7Ual53T8zud+twfPVX1T0bjw/+zbLjrNY2BcDh1RFLDwFwPhzVal53v9akj64t5EuNBYRu03hr4P3tQIBtUwAcUV146SEAzoeLtprX3a8mAJbwlepl1Q2rB1Yfbou/D9sUABevLrX0EADnwxWrfSs4ztezrv2Svl49rbpBY6+Bjy07znmzrgA4fgXHOLw6ZgXHAVjKql7DvtxY2pZlfamx2+DNGosKfXzRac6ldQXAF1d0nCu0mnoGWLdjqous6Fgfb/J17DfMR6v7VT/Z2HXwC4tOc5DWFQDHreg416ouvaJjAazT97a6168PZY/7TfTm6h7VT1d/3IbfKLhtAXC96qorOhbAOt2kcS/TKnykLb75bAKvbkTAnQ/8fSNtWwAcXV13RccCWJfDq+u3utfcT67oOOydE6s/rX6msbzwW5Yd58zWFQAfXuGxblldcoXHA9hr/7m69oqOdVz1mRUdi733ucbbAXdqLCj0L8uOc6p1BcA/tLqbIm5TXX1FxwJYh1tXl13RsY7NFYBt9KnquY0YfFgbcKPgugLgHa3uH+wR1R2qw1Z0PIC9dK3GlctVPcH0zlwB2GafqZ5UfXf11OrflxpkXQHwsVa7UMK9qqut8HgAe2FfY5/5Vd27tL+xW51NhbbbKdW/Vg+ovr9xr8DarwiscyXAt7e6lasuXt2/7VrJEJjPdzSeDV+Vj1QfWOHxWN57GvcH3Kl6bWNxoVrH3jf79+/f848DrlN9ulGuq/g4pVFOAJvokOr3Wt1r3v7GIjMXWuPXwPrdpfEL83v2/Ny8xgCosa3iKn8Y3l5dYs+/HQDn3g83FutZ1evdidW91/oVsJTLVjfatQB4QOMf8Soj4Kl7/70AOFe+tXFpd5Wvde+prrzOL4Jl7VoAXLJxB+QqfyhOqe67998KgINyePVnrfZ1bn/17HV+ESxv1wKgxhaKq/7B+ELjfROApT2z1b/GfabxKCET2cUAuEr1lVb/A/KpxuM2AEt5cuNpp1W/vr2icVMhE9nFAKh6Rqv/AflmJf9EtgwG1uuQxm/+J7X617XPVz+0vi+FTbGrAXCt6oT2JgK+Vj0kj8oA63H59uY9/29+vG59XwqbZFcD4NDqCe3dD8z+6jnZOhjYO4dVt6r+qb17HftC1juZ1q4GQNXlGrsE7mUE/FNjFS67BwKrdM3qie3NJf/Tfjx5XV8Qm2eXA6DGHsmntLc/QPurl1W3ry66ym8OMJ3vaLzF+MH2/nXrw9Wl1vJVsZF2PQAu3Dg57/UP0v5GaLy8uk+2EwYO3iHVD1aPrd7Vel6v9jd+QWJie31u3ncOJ+iV2LfvbG/Kv1YjAq6554Oc6tjGD/K7G8sJv6/68ho/P7DZrlBdr7phdf3G1q3HrPHzP7O6X+MXFya11+fnTQiAGo/uPb26yJ4Pc3onVx9vPD74xeoT1XGN9bu/lt0GYQb7qqMabxFevrpM49L7MdXRC8zz1urHGpunMbFZAuDQ6vHVw/Z8mINz0oEP6wnAHA6pLrj0EI27/n+setPSg7C8WQKgxnP7L65+dG+nAdhI+xv7mjzrwN+Z3F6fnzfpEvfx1T2rNy49CMCanVw9Kid/1miTAqDG+/A/U71j6UEA1uSU6ner38jJnzXatACo+ljjSsA/Lj0IwB7b37gB+heXHoT5bGIAVL23+vHqLUsPArBH9le/Uz2o8RYArNWmBkCNlbbumo0wgN1zcvXoxpNPnvVnEZscADXeDvjJ6vlLDwKwIp+r7t949BkWs+kBUGMv7PtXD28szgOwrd5a3bmx0h8sapPWATgYN62eVN1gVQcEWJNnVY9rXNmEczTTQkAH61uqRzSuChyyygMD7IGPV4+sXpGrmJwLAuCsHVZ9T2M/7hu3GUt4ApzW8dVzq1+rPrXsKGwjAXD2Dq8eUD2wsXHH4Xv1iQAO0leqv68eU/2vhWdhiwmAg3NkYw3te1ZXTwgA6/e5xiqmT6tetfAs7AABcO4c2bg34Ierq1ZXXNcnBqb1gerd1R9Vr114FnaIADjv/lN12+om1bWrSy4xBLCTPtw46b+1enUjAmClBMD5d5nqhtW1GiFw3QN/P2zJoYCt8tnqPdX7qvdXx1Z/t+RA7D4BsFqHVN9eXbkRBscc+Pu3VUdXR1VHNN5KOCqRALtuf/X1xuN5xx/4+HL1yepfD3x8+sD//lDu5meNdiIAAIDNsg1LAQMAKyYAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJiQAAGBCAgAAJvR/AL9O+A1wSWwmAAAAAElFTkSuQmCC)
        }

        @media screen and (max-width:768px) {

            #paginationInfo,
            .button-row,
            .jump-row {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 0
            }

            .chat-video video {
                margin-right: 8px;
                margin-left: 8px;
                max-width: 280px
            }

            button {
                padding: 5px 10px;
                background-color: #3498db;
                color: #fff;
                border: 0;
                border-radius: 4px;
                cursor: pointer;
                margin: 0 5px;
                transition: background-color .3s
            }

            button:hover {
                background-color: #2980b9
            }

            input {
                padding: 8px;
                width: 120px;
                box-sizing: border-box;
                margin-right: 0;
                margin-left: 5px
            }
        }

        .call i,
        .system-msg>.emoji_img {
            width: 18px;
            height: 18px
        }

        .emoji_img {
            width: 22px;
            height: 22px;
            vertical-align: middle;
            margin-top: -4.4px
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 9999;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, .8)
        }

        .modal-image {
            display: block;
            max-width: 90%;
            max-height: 90%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%)
        }

        @media screen and (max-width:768px) {
            body {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center
            }

            ::-webkit-scrollbar {
                display: none
            }

            .page {
                width: 100%;
                height: 100%;
                box-shadow: none
            }

            .page .mid-bar,
            .page .side-bar {
                display: none
            }

            .page .main-body {
                height: 95%
            }

            .page .main-body .nav-bar .turner-bar {
                transform: none;
                font-size: 12px
            }

            .page .main-body .nav-bar .turner-bar .button {
                margin: 10px 5px
            }

            .page .main-body .nav-bar .turner-bar input {
                font-size: 14px
            }
        }

        .OpenIM-card,
        .personal-card {
            position: relative;
            width: 220px;
            height: 115px;
            border-radius: 5px;
            background-color: #fff
        }

        .personal-card .contner {
            display: flex;
            margin: 12px 16px 5px;
            width: 100%;
            height: 68px
        }

        .OpenIM-card .contner img,
        .personal-card .contner img {
            line-height: 100px;
            width: 50px;
            height: 50px;
            margin: 2px 0 0;
            border-radius: 5%;
            overflow: hidden
        }

        .personal-card .contner .text {
            width: 60%;
            margin-left: 10px;
            overflow: hidden
        }

        .personal-card .contner .text .nickname {
            font-size: 14px;
            line-height: 20px;
            color: #000
        }

        .personal-card .contner .text .nickname img {
            width: 14px;
            height: 14px;
            margin: 0
        }

        .personal-card .contner .text .other {
            font-size: 12px;
            line-height: 16px;
            color: #9e9898
        }

        .personal-card .bottom {
            width: 100%;
            height: 30px;
            font-size: 12px;
            color: #9e9898;
            background-color: #fafafa;
            border-top: #eee 1px solid
        }

        .personal-card .bottom .text {
            font-size: 12px;
            line-height: 28px;
            margin-left: 16px
        }

        .personal-card:hover {
            background-color: #333
        }

        .item-left .personal-card {
            margin-left: 15px;
            background-color: #fff
        }

        .item-left .personal-card:before,
        .item-right .personal-card:before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent
        }

        .item-left .personal-card:before {
            left: -18px;
            border-left: 10px solid transparent;
            border-right: 10px solid #fff
        }

        .item-right .personal-card {
            margin-right: 15px;
            background-color: #fff
        }

        .item-right .personal-card:before {
            border-left: 10px solid #fff;
            border-right: 10px solid transparent;
            right: -18px;
            top: 11px
        }

        .OpenIM-card .contner {
            display: flex;
            margin: 12px 16px 5px;
            width: 100%;
            height: 68px
        }

        .OpenIM-card .contner .text {
            width: 60%;
            margin-left: 10px;
            overflow: hidden
        }

        .OpenIM-card .contner .text .nickname {
            font-size: 14px;
            line-height: 20px;
            color: #000
        }

        .OpenIM-card .contner .text .desc {
            font-size: 14px;
            line-height: 20px;
            color: #9e9898
        }

        .OpenIM-card .contner .text .desc img {
            width: 14px;
            height: 14px;
            margin: 0px 0px 0px 0px;
        }

        .OpenIM-card .contner .text .nickname img {
            width: 14px;
            height: 14px;
            margin: 0px 0px 0px 0px;
        }

        .OpenIM-card .contner .text .other {
            font-size: 12px;
            line-height: 16px;
            color: #9e9898;
        }

        .OpenIM-card .bottom {
            width: 100%;
            height: 30px;
            /* margin-top: 6px; */
            font-size: 12px;
            color: #9e9898;
            background-color: #fafafa;
            border-top: #eeeeee 1px solid;
            border-radius: 0 0 5px 5px;
        }

        .OpenIM-card .bottom .text {
            font-size: 12px;
            line-height: 28px;
            margin-left: 16px;
        }

        .OpenIM-card:hover {
            background-color: #333;
        }

        .item-left .OpenIM-card {
            margin-left: 15px;
            background-color: #fff;
        }

        .item-left .OpenIM-card {
            margin-left: 15px;
            background-color: #fff;
        }

        .item-left .OpenIM-card:before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-top: 10px solid transparent;
            border-right: 10px solid #fff;
            border-bottom: 10px solid transparent;
            left: -18px;
        }

        .item-right .OpenIM-card {
            margin-right: 15px;
            background-color: #fff;
        }

        .item-right .OpenIM-card:before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-left: 10px solid #fff;
            border-top: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid transparent;
            right: -18px;
            top: 11px;
        }

        /* 位置 */
        .location {
            position: relative;
            width: 220px;
            height: 180px;
            border-radius: 5px;
            background-color: #fff;

        }

        .location .poiname {
            width: 90%;
            padding: 0 16px 0 10px;
            margin: 5px 0 5px 0;
            font-size: 14px;
            line-height: 20px;
        }

        .location .label {
            width: 100%;
            padding: 0 16px 0 10px;
            font-size: 12px;
            color: #9e9898;
            ;
        }

        .location .map {
            width: 100%;
            height: 120px;
            overflow: hidden;
        }

        .custom-content-marker {
            position: relative;
            width: 25px;
            height: 34px;
        }

        .custom-content-marker img {
            width: 100%;
            height: 100%;
        }

        .item-left .location {
            margin-left: 15px;
            background-color: #fff;
        }

        .item-left .location:before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-top: 10px solid transparent;
            border-right: 10px solid #fff;
            border-bottom: 10px solid transparent;
            left: -18px;
            top: 11px;
        }

        .item-right .location {
            margin-right: 15px;
            background-color: #fff;
        }

        .item-right .location:before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-left: 10px solid #fff;
            border-top: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid transparent;
            right: -18px;
            top: 11px;
        }

        /* 合并消息 */
        .merge-message {
            position: relative;
            width: 220px;
            height: 110px;
            border-radius: 5px;
            background-color: #fff;
        }

        .merge-message .title {
            width: 100%;
            margin: 10px 16px;
            font-size: 14px;
            line-height: 20px;
            color: #000;
        }

        .merge-message .msg {
            height: 16px;
            margin: 4px 16px;
            font-size: 12px;
            line-height: 16px;
            color: #9e9898;
            overflow: hidden;
        }

        .merge-message .bottom {
            width: 100%;
            height: 28px;
            margin-top: 6px;
            font-size: 12px;
            color: #9e9898;
            background-color: #fafafa;
            border-top: #eeeeee 1px solid;
        }

        .merge-message .bottom .text {
            font-size: 12px;
            line-height: 28px;
            margin-left: 16px;
        }

        .merge-message:hover {
            background-color: #333;
        }

        .item-left .merge-message {
            margin-left: 15px;
            background-color: #fff;
        }

        .item-left .merge-message:before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-top: 10px solid transparent;
            border-right: 10px solid #fff;
            border-bottom: 10px solid transparent;
            left: -18px;
            top: 11px;
        }

        .item-right .merge-message {
            margin-right: 15px;
            background-color: #fff;
        }

        .item-right .merge-message:before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-left: 10px solid #fff;
            border-top: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid transparent;
            right: -18px;
            top: 11px;
        }

        .search-modal,
        .merge-msg-modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgb(0, 0, 0);
            background-color: rgba(0, 0, 0, 0.4);
        }

        .search-modal-content,
        .merge-msg-modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            /* 15% 从顶部和居中 */
            padding: 20px;
            border: 1px solid #888;
            width: 60%;
            height: 80%;
        }

        /* 关闭按钮 */
        .merge-msg-modal-content .title .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            margin-top: -20px;
            margin-right: -10px;
        }

        .merge-msg-modal-content .title .close:hover,
        .merge-msg-modal-content .title .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        .merge-msg-modal-content .title {
            height: 40px;
            font-size: 14px;
            color: #9e9898;
            border-bottom: #eeeeee 1px solid;
        }

        .modal-container {
            /* display: flex; */
            /* position: fixed; */
            height: 90%;
            overflow: hidden;
            overflow-y: auto;
        }

        .modal-container .OnePersonmsg {
            display: flex;
            overflow: hidden;
            overflow-y: auto
        }

        .search-modal-content .modal-container .OnePersonmsg:hover {
            background-color: #f0f0f0;
        }

        .search-modal-content .modal-container .OnePersonmsg {
            cursor: pointer;
        }

        .tooltip {
            position: absolute;
            background-color: black;
            color: white;
            padding: 5px;
            border-radius: 3px;
            font-size: 12px;
            white-space: nowrap;
            /* 防止提示文本换行 */
            display: none;
            /* 默认隐藏 */
            z-index: 2;
        }

        .search-modal-content .modal-container .OnePersonmsg:hover .tooltip {
            visibility: visible;
        }

        .OnePersonmsg .left {
            width: 12%;
            text-align: right
        }

        .OnePersonmsg .right {
            width: 88%
        }

        .OnePersonmsg .left .avatar {
            margin-left: 30px;
            margin-top: 10px
        }

        .OnePersonmsg .left .avatar img {
            width: 36px;
            height: 36px;
            user-select: none
        }

        .OnePersonmsg .right .msg-block {
            width: 80%;
            margin-left: 10px;
            margin-top: 10px;
            padding-bottom: 20px;
            border-bottom: #eee 1px solid
        }

        .msg-block .msg-container-top {
            font-size: 14px;
            color: #9e9898;
            display: flex;
            justify-content: space-between;
            align-items: center
        }

        .msg-block .msg-container {
            font-size: 14px;
            line-height: 20px;
            color: #000
        }

        .msg-block .msg-container img,
        .msg-block .msg-container video {
            max-width: 280px
        }

        .msg-block .inner-msgMerge-card {
            background-color: #f4f4f4;
            padding-top: 10px;
            height: 60px
        }

        .inner-msgMerge-card .desc,
        .inner-msgMerge-card .title {
            height: 20px;
            margin: 0 0 0 10px;
            font-size: 14px;
            line-height: 18px;
            color: #000;
            overflow: hidden
        }

        .inner-msgMerge-card .desc {
            height: 16px;
            font-size: 12px;
            line-height: 16px;
            color: #9e9898
        }

        .modal-search-box {
            width: 90%;
            padding: 10px;
            margin: 10px auto;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .close-btn {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-btn:hover,
        .close-btn:focus {
            color: black;
            text-decoration: none;
        }

        .mini-program,
        .video-number {
            position: relative;
            width: 220px;
            border-radius: 5px;
            background-color: #fff
        }

        .mini-program a {
            text-decoration: none
        }

        .mini-program .top {
            display: flex;
            margin: 0;
            width: 100%;
            height: 40px
        }

        .mini-program .top img {
            margin: 15px 0 0 15px;
            overflow: hidden
        }

        .mini-program .top .text {
            width: 60%;
            margin: 15px 0 0 10px;
            font-size: 12px;
            line-height: 20px;
            overflow: hidden;
            color: #9e9898
        }

        .mini-program .title {
            font-size: 14px;
            line-height: 20px;
            color: #000;
            margin: 0 0 0 15px
        }

        .mini-program .cover,
        .mini-program img {
            width: 190px;
            height: 170px;
            margin: 10px auto;
            color: #9e9898
        }

        .mini-program img {
            margin: 0 auto
        }

        .mini-program .bottom {
            width: 100%;
            height: 30px;
            font-size: 12px;
            color: #9e9898;
            background-color: #fafafa;
            border-top: #eee 1px solid;
            border-radius: 0 0 5px 5px
        }

        .mini-program .bottom .text {
            font-size: 12px;
            line-height: 28px;
            margin-left: 16px
        }

        .mini-program:hover {
            background-color: #333
        }

        .item-left .mini-program {
            margin-left: 15px;
            background-color: #fff
        }

        .item-left .mini-program:before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-top: 10px solid transparent;
            border-right: 10px solid #fff;
            border-bottom: 10px solid transparent;
            left: -18px
        }

        .item-right .mini-program {
            margin-right: 15px;
            background-color: #fff
        }

        .item-right .mini-program:before {
            border-left: 10px solid #fff;
            border-right: 10px solid transparent;
            right: -18px;
            top: 11px
        }

        .video-number {
            width: 210px;
            height: 340px;
            background-color: #a56819;
            display: flex;
            flex-direction: column
        }

        .video-number .title {
            width: 90%;
            height: 60px;
            font-size: 14px;
            line-height: 20px;
            overflow: hidden;
            color: #333;
            margin: 5px auto
        }

        .video-number .container {
            width: 210px;
            height: 280px;
            overflow: hidden
        }

        .video-number .container .bottom {
            position: absolute;
            top: 240px;
            width: 100%;
            height: 100px;
            font-size: 12px;
            line-height: 16px;
            margin: auto 0 -100px;
            background: linear-gradient(to bottom, transparent, #000);
            border-radius: 0 0 5px 5px
        }

        .video-number .container .bottom .author {
            position: absolute;
            width: 100%;
            height: 30px;
            margin: 0 0 0 15px;
            bottom: 10px;
            overflow: hidden
        }

        .video-number .container .bottom .author img {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block
        }

        .video-number .container .bottom .author .authIcon,
        .video-number .container .bottom .author .logo {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            vertical-align: middle
        }

        .mini-program .top img,
        .video-number .container .bottom .author .authIcon img,
        .video-number .container .bottom .author .logo img {
            width: 20px;
            height: 20px;
            border-radius: 50%
        }

        .video-number .container .bottom .author .name {
            height: 30px;
            font-size: 14px;
            line-height: 30px;
            color: #fff;
            margin: 0 4px;
            display: inline-block;
            vertical-align: middle
        }

        .item-left .video-number {
            margin-left: 15px;
            background-color: #fff
        }

        .item-left .video-number:before,
        .item-right .mini-program:before,
        .item-right .video-number:before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent
        }

        .item-left .video-number:before {
            left: -18px;
            border-left: 10px solid transparent;
            border-right: 10px solid #fff
        }

        .item-right .video-number {
            margin-right: 15px;
            background-color: #fff
        }

        .item-right .video-number:before {
            border-left: 10px solid #fff;
            border-right: 10px solid transparent;
            right: -18px;
            top: 11px
        }

        .bubble-audio-left,
        .bubble-audio-right {
            position: relative;
            min-width: 40px;
            max-width: 250px;
            border-radius: 5px;
            line-height: 40px
        }

        .bubble-audio-left {
            background: url(data:image/png;base64,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) no-repeat center left 10px;
            background-size: 20px;
            padding-left: 30px;
            background-color: #fff
        }

        .bubble-audio-right {
            background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAGx0lEQVR4nO2deaiVRRjGf1ctLbXslrYokVwrkxZDVMrECo2ihXZbKG2TstSorNDqnxZSKiupLCtLMSUthVYtbZGihWwTrdAM2sgMs9Tsqt0YeIXD5XruM+d+M993LvP7Rzz3OXPmnOd8M/O97ztzSCQSiUQikUgkEolEIpFIJBKJRCugJuRbqKurq/SpbYGZwGLg+Wx71XLWrFkTrO028d6GjDPjBeASYAYwpoB9DEbRDHFX7NPAhSX/fxSYmHO/olE0Q+4DRjbx+D3ApNBDbBEokiFXAreX+futwOMFHWYzoyhvbhDwhKC71ib7dhH6lAttQ75obW2tIusKLAG6iM0ebf++W3nPWsaGDRuCtZ33FVJjy9qDPJ7zPjA5YJ9yJW9DRgOneeg/Bs4ANgfsU67kaUhP4H4P/WrgTODvgH3KnTwNmQp0ErUb7cr4PXCfcicvQ84CThe1DcDFwLeB+1QI8jBkN+AhD/0DwBsB+1Mo8jDkahd3FLVfeIZNdrfQy4AK+5Y7sQ3p4PEBbweuALaJendPNQe4CngbOLEF/cyN2IZcBnQXtY/ZFaLyCHCuaTsDr3vMU4UhpiHutW4RteuAuzzaHgFc3+ixPYCXgeEe7eROTEOGAYeJWhf1/UvU9rWgY1O4OWW2DWNVQUxDRom6n4FporY9MBfYs4zGzS3TgRvFNnMlliH72V22wsPAv6J2AnC4oHMxsyk2tBWaWIacY/cfzbHJ4+roDdzm0YePbE4pNLEMUSfWOWaKwiQbshRWAadWQxwshiEuXnWCqJ0h6gZY+EXBmXCexcMKTwxDhorf5LU2rCioy2esamVVZV2PTwxD1DtmN743CLruNicpLLGUb9UQwxB1uFok6i4Xc+r/AWNFkwtDaEPc3fIxgs4tc5eJbaoLhHnASlHrbiAfBA4U9cEIbUgf8dv8GbBV0B0sGoytwlSeBG6yL8UhHs/LnNCGHCnqPhV1p4i65cDnonZkSXFenZnSW3xu5oQ2pJeo+1rUDRJ1L4q6WkuAldIDeA84VmwjU0Ib0lPUfSPq+om6t0TdOGDfJh7vBiwFjhfbyYzQhqj1VmsFTVtxKNko5lFce9eU+XsX2w4xVGgrM0Ib0lXU/SZouovxsJW25G2O44RVVUfgVeBsob1MCG3IPoJmC7BD0Knmfifq1PsjF2WYb9nO4IQ2RKm7UgN+nUWdWrulhO130tZKXkd7PKciilD9rkZ3VUNUg9Urbic1lucvt2WixRTBELWqRKWqQiWNKcI+i3Lp11KyvpL+EHU7cUbfUCZ/nwmhrxClSl01RB2Kuok69d4HW3SMCG0GEQxRdrbUiv1YL77moaLuA1FXD1wAzBL1LSK0IcqKx/XhAEH3k1UzNkcf8X19KNz/bLHijAVCe5kQ2pBfRJ0SYd0uZv66iBHh7VYHvCv+tFqyxUJbmRHakB9E3RGibrmoGybqpuxiWHWVkyfbVRSV0IasFnVHiTr1Azpf1LmV1vhGj7mhcYhH+D5TQhvylajrL+rUKG5/j0TWMyXnqbhDTAZ7rsAyJbQhK8U4VT/bqtAcLiq8QnxtnyK6UTZ8DfYYZoMQ2pB/gC8FXXuPYN9cUTfcI/NXbyncX0V9MGKETtT1vqssVJgpXnVtbGNpVRHDkKWizm22UQ6X+RFYKLY51MqGqoYYhrwjBhBduneg2ObdHkHEqZ6h9lyJYchGj5qrpo5mago3L70iaveyqsi9RX2uxAq/zxN1l3ocJjBRDKVg4ZQFHoHM3IhlyHzxw+tkRzAprPCctE8qOamusMQyZL3HEDPOY9/HnXYz1xwNdkf+XGXdj0fMjOF0UdfD4yrZbIdl1pfR7LD2GhfEFZKYhizyiG1NsMlY4ZMy+0W2WbXIU9m8hfDEPFGuwarclc2fHW3YUkPfzpT9G8XEtlpi6SW/XjdPazpRbpZHeGKs7UFXGVOSSNpkpzio81ZhiG2Ii23dK2rb2Z5DpVoRmysuAp61fIgaISgUeZQBucn9e1Hb187sVam3UxvUvYqFIw9D3Id2s4d+vOe5jFVNXoVyCz32FNbY/nX1nJSqJs/Kxes8it9cHOq1Cso/q448DVnrWSfby1ZNHQP2KXfyru11lYBveugH2pXSak3J25AGK9H0SZ0OsYP5WyVFqH5fZ/nvcvGoUmZbgqpVUpRfR1jWxBF9TTHNUrJqHqTqKNJvcbiyznJHj0+2HUzK/sGqpWg/jjJhF1Xmd1idVVVvxlEo2g+jNNhZvR0sUttgCauqK+eplCL+Us0OO+t9m5WOFj7Ll0gkEolEIpFIJBKJRCKRSCQSiUQJwP+9HRZTetAJJgAAAABJRU5ErkJggg==) no-repeat center right 10px;
            background-color: #9eea6a;
            background-size: 20px;
            padding-right: 30px;
            direction: rtl
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg)
            }

            to {
                transform: rotate(360deg)
            }
        }

        .item-left .bubble-audio-left {
            margin-left: 15px;
            background-color: #fff
        }

        .item-left .bubble-audio-left:before,
        .item-right .bubble-audio-right:before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
            top: 11px
        }

        .item-left .bubble-audio-left:before {
            left: -18px;
            border-left: 10px solid transparent;
            border-right: 10px solid #fff
        }

        .item-right .bubble-audio-right {
            margin-right: 15px;
            background-color: #9eea6a
        }

        .item-right .bubble-audio-right:before {
            border-left: 10px solid #9eea6a;
            border-right: 10px solid transparent;
            right: -18px
        }

        .custom-menu-item {
            background-color: #fff;
            padding: 8px 12px;
            cursor: pointer
        }

        .custom-menu-item:hover {
            background-color: #ddd
        }

        .topnavbar-nav {
            display: flex;
            flex-wrap: wrap;
            align-items: center
        }

        .topnavbar-nav .menu-button {
            max-width: 100px;
            height: 30px;
            font-size: 14px;
            line-height: 30px;
            text-align: center;
            background-color: #fff;
            padding: 0 10px;
            border-radius: 5px;
            margin: 5px 10px;
            cursor: pointer;
            overflow: hidden;
            z-index: 999
        }

        .topnavbar-nav.show {
            display: flex
        }

        .menu-toggle {
            display: none;
            font-size: 1.5em;
            cursor: pointer;
            align-self: flex-start
        }

        @media screen and (max-width:768px) {
            .merge-msg-modal-content {
                background-color: #fefefe;
                margin: 5% auto;
                padding: 20px;
                border: 1px solid #888;
                width: 90%;
                height: 80%
            }

            .OnePersonmsg .left .avatar {
                margin-left: 0;
                margin-top: 10px
            }

            .topnavbar-nav {
                display: none;
                flex-direction: column;
                margin-left: auto;
                margin-right: -120px
            }

            .topnavbar-nav,
            .topnavbar-nav .show {
                align-items: flex-start
            }

            .menu-toggle {
                display: block;
                margin-left: auto;
                margin-right: 20px
            }
        }

        .loader {
            border: 16px solid #f3f3f3;
            border-top: 16px solid #dd512e;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 2s linear infinite;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%)
        }
    </style>
    <title>出错了</title>
</head>

<body>
    <!-- 加载动画容器 -->
    <div class="loader" id="loader"></div>
    <div class="page" id="content" style="display: none;">
        <!-- <div class="side-bar"></div> -->
        <div class="mid-bar">
            <div class="timeline-area">
                <div class="timeline-wrapper">
                    <div class="timeline" id="timeline">

                    </div>
                </div>
            </div>
        </div>
        <div class="main-body">
            <div class="title-bar" id="title-bar">
                <p id="title">出错了</p>
                <div class="topnavbar-nav" id="topnavbar-nav">
                </div>
                <span class="menu-toggle" id="menu-toggle">
                    <i class="fas fa-bars"></i>
                </span>
            </div>
            <div class="container">
                <div class="content" id="chat-container" onscroll="checkScroll()">
                    <div class="item item-center">
                        <span>8:16</span>
                    </div>
                    <div class="item item-center">
                        <span>错误提示</span>
                    </div>
                    <div class="item item-left">
                        <div class="avatar">
                            <img
                                src="https://blog.lc044.love/static/img/a774ab7a32635db7b4254c8ff7caaa89.Camera_XHS_16984826124131040g2sg30qo1ggopgqe05oce.webp">
                        </div>
                        <div class="bubble bubble-left">
                            很抱歉，由于程序出现未知错误，您的聊天记录未能成功展示。<br />您可以关注下方微信公众号，<b>回复：联系方式</b>，获取QQ交流群群号，并在群内寻求帮助。
                        </div>
                    </div>
                    <div class="item item-left">
                        <div class="avatar">
                            <img
                                src="https://blog.lc044.love/static/img/a774ab7a32635db7b4254c8ff7caaa89.Camera_XHS_16984826124131040g2sg30qo1ggopgqe05oce.webp">
                        </div>
                        <div class="bubble bubble-left">您可自行排查问题所在，例如只选择文本或图片，反馈时请说明哪种消息类型出现了问题！！！
                        </div>
                    </div>
                    <div class="item item-left">
                        <div class="avatar">
                            <img src="https://blog.lc044.love/static/img/a774ab7a32635db7b4254c8ff7caaa89.Camera_XHS_16984826124131040g2sg30qo1ggopgqe05oce.webp"
                                loading="lazy">
                        </div>
                        <div class="content-wrapper content-wrapper-left">
                            <div class="chat-image">
                                <img src="https://blog.lc044.love/static/img/3fd32f1732a2c8f53a7eb923472b8f19.clipboard-2023-12-18.webp"
                                    onclick="showModal(this)" loading="lazy">
                            </div>
                        </div>
                    </div>
                    <div class="item item-right">
                        <div class="bubble bubble-right">好的</div>
                        <div class="avatar">
                            <img src="https://cdn.luogu.com.cn/upload/image_hosting/rbnmv4bu.png">
                        </div>
                    </div>
                </div>
            </div>
            <div class="nav-bar">
                <div class="turner-bar">
                    <div class="button pre-page" onclick="prevPage()">上一页</div>
                    <div class="navigator-line">转到第
                        <input class="navgator" value="1" id="gotoPage" onchange="gotoPage()"></input>页，共<p
                            id="maxPage">0</p>页
                    </div>
                    <div class="button next-page" onclick="nextPage()">下一页</div>
                </div>
            </div>
        </div>
    </div>
    <div id="search-modal" class="search-modal">
        <div class="search-modal-content" id="search-modal-content">
            <span class="close-btn" onclick="closeSearchModal()">&times;</span>
            <input type="text" id="searchBox" class="modal-search-box" placeholder="Type your search...">
        </div>

    </div>
    <div id="modal" class="modal" onclick="hideModal()">
        <img id="modal-image" class="modal-image">
    </div>
    <script>
        const FileIcons = {
            'DOCX': 'data:image/png;base64,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',
            'XLS': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsCAYAAAB5fY51AAAXlUlEQVR4nO3dfZBV9X3H8d/FsU3UFjOahtjobjQZoqZAouDDGsE0bDCtI4tpBhoftqWdQmPUBuwEq5EkJqQVMGgNMNGIaBI6UXftGELBSVArsRHBhI41f6ArGUfaxEYQEOTh5vu5urqu95z7dJ5+9/d+zeCeszPuw9097/s7v3t+Z0uuYCb3/eU4N8JNdOVD42y307mSvS0f7YAUnN/x8T0/Gljfta7ne0/aLgquZP9yN6VveuehEaUrXbk8texcpwMysrDrOvd/r/z/q//yxNIziFbxlexfbixUkw6VSleWXXmq7QKZU7DGHHuK++HAgztve/K7Hf09K16yd6OgcgnW1L7eo3eX9t5km70OyNFgsOTWLXdue3Dr+rFEq7gyD1Z334yp5ZK7g3kpFMHQYAnRKrZMg9V9/4ybyuXyVbYJFMLwYImidfmY3g7bRMFkFqzJ/dPvsDe9DiiQasGSeRsW/Ps3uq650DZRIJkEi1ihqKKCtWv/bve1x28mWgWTerCIFYosKlhCtIon1WBN7pvea59BwQIKKS5Yomgt3rR8+fVnzpllu8hZasGa0je982CptJlXA1FktYIlW3cMuAUbb/7c7Z+46Vu2ixylFiw7FfyJvZnkgAKrJ1hCtIohlWC9dq1Vuc82gUKrN1hCtPKXTrD6pz9bdq7TAQXXSLBky4tPH5jzyFfHs+4wHyX7lyibu5p0sOR+YptA4TUaLHnwV//JYumcJB6s7r7p/eWSu9A2gcJrJlhCtPKRaLBeX9T8W9sEvNBssETRWvTEraPX9KwacMhEosHiuiv4ppVgyZ1P/+D5/qdXf5jF0tlINFh2OrjCTgcvs03AC60GS7RYmjs8ZCPRYE3un7HZufI42wS8kESwhGhlI+FgTS/bG8AbSQVLvvPUqk0zT51xmm0iJYkFa0plKY571jYBbyQZLK07ZLF0upIM1iQL1k9sE/BGksESopUugoWgJR0sIVrpIVgIWhrBEkXrqoevY91hwggWgpZWsITF0skjWAhamsESopUsgoWgpR0sUbRu2XL7BUs+dsMDtosWECwELYtgyWPbN+3/0mOLJrBYujUEC0HLKliixdLc4aE1BAtByzJYQrRaQ7AQtKyDJT8ceHDnbU9+t4N1h40jWAhaHsESFks3h2AhaHkFS4hW4wgWgpZnsETRunxMb4dtog4EC0HLO1iyYOMta64Zf8X5tokaCBaCVoRgad0hi6XrQ7AQtCIES4hWfQgWglaUYImitXjT8uXXnzlnlu2iCoKFoBUpWKJ1hyyWjkawELSiBUuIVjSChaAVMVhCtKojWAhaUYMlz+7cdvDvfjzvdNYdvolgIWhFDpawWPqtCBaCVvRgCdF6E8FC0HwIlihai564dfSanlUDLmAEC0HzJVhy79bV2+/e8oOTQ14sTbAQNJ+CJVosHfIdHggWguZbsCTkaBEsBM3HYMl3nlq1aeapM06zzaAQLATN12Bp3WGIi6UJFoLma7AkxGgRLATN52BJaNEiWAia78ESRevrG2++esHZ1yy03bZGsBC0dgiWhLJYmmAhaO0SLAkhWgQLQWunYImidcuW2y9Y8rEbHrDdtkOwELR2C5Y8tn3T/i89tmhCOy6WJlgIWjsGS7RYuh3v8ECwELR2DZa0Y7QIFoLWzsGS9c9v2LXk8W8f3y7rDgkWgtbuwZJ2WixNsBC0EIIl7RItgoWghRIsUbQuH9PbYZveIlgIWkjBkgUbb1lzzfgrzrdNLxEsBC20YGndoc+LpQkWghZasMTnaBEsBC3EYImitXjT8uXXnzlnlu16g2AhaKEGS7bv+bW79qcLvFosTbAQtJCDJVos7dMdHggWgnbj2de6se8+1bbC5VO0CBaC9tkP9rjLTvmMbYXt2Z3bDn57y3fPW3DONY/YbmERLATtvPec6eadeaVtwYfF0gQLQTv+90e526fcZFuQokeLYCFor+7d6x6Ydrc76vAjbQ+iaC164tbRa3pWDbiCIVgI2sEDB9yXz5jrznnfGbaHQfduXb397i0/OLloi6UJFoJ37rHj3bVdX7AtDKXF0kW7wwPBQvB279zh/mP6PXZaeITtYaiiRYtgIXiax/rihM+7Ke//uO1huO88tWrTzFNnnGabuSNYCF65XHZHHfw913fRSttDNfM2LCjEYmmCBZg9L+90/9r9z27cH33Y9jCcFksX4Q4PBAswerXw6NKR7t5pKxyqK0K0CBbwOo2yrjtnrvtk53m2h2oUra9vvPnqBWdfs9B2M1eyf4kgWPDdoYMHnXvlgLv/M3e7I3nFMFKei6UJFjDE3j273Tl/fIb7+rn/ZHuIkle0CBYwzMs7XnJzz/qcu+iDf257iKJo3bLl9guWfOyGB2w3EwQLGEanhorW4k9+zXUdN97egyhbXnz6wJxHvjo+q8XSBAuo4sD+/e6w/WW37FOL3UkjO+w9iKLF0lnd4YFgARF0Bfzhh0YQrTpkFS2CBcRQtPbu3eP+sesKN+0Df2bvQZT1z2/YteTxbx+f5rpDggXUsP/VfW7Prl2u55QL3Oc/OpNLHmKkvViaYAF10JXwmojveNfx7m9Pu8x1nzDR3otq0owWwQLqpEXSu3buqMTrrI4J7q/HftaNDfhPhMVRtC4f09thm4kiWECD9r3yintlz27bcq7zmA43c9zF7pzjJnCqOMytv1jx48vH/tWf2mZiCBbQBF2rtXvXy+7A/ldtz7kRIw5zZ54w3p17wlnuA+86kZGX0brDpBdLEyygBYMT8ocOHbS9Nylg7xt5nDvuD0a5j44aa+95zYgRIyxo73eh/NELRet7/3PP9G994sZ/s92WESwgAbr8QaeJw8MF5975jnf+wyMX//CbttkyggUkSOHat2/vG6eKIFhA4emVxFctXPssYOXyIXtPuAgW4BHNc2lt4qv79gV5ykiwAE/p1cVDh8IacR3uShes/Yt7ErkFDcECkKrDyu68NT2r1rsEECwAqSJYALxBsAB4g2AB8AbBAuANggXAGwQLgDcIFgBvECwA3iBYALxBsAB4g2AB8AbBAuANggXAGwQLgDcIFgBvECwA3iBYALxBsAB4g2AB8AbBAuANggXAGwQLgDcIFgBvECwA3iBYALxBsAB4g2AB8AbBAuANggXAGwQLgDcIFgBvECwA3iBYALxBsAB4g2AB8AbBAuANggXAGwQLgDcIFgBvECwA3iBYALxBsAB4g2AB8AbBAuANggXAGwQLgDcIFgBvECwA3iBYALxBsAB4g2AB8AbBAuANggXAGwQLgDcIFgBvECwA3iBYALxBsAB4g2AB8AbBAuANggXAGwQLgDcIFgBvECwA3iBYALxBsAB4g2AB8AbBAuANggXAGwQLgDcIFgBvECwA3iBYALxBsAB4g2AB8AbBAuANggXAGwQLgDcIFgBvECwA3iBYALxBsAB4g2AB8AbBAuANggXAGwQLgDcIFgBvECwA3iBYALxBsAB4g2AB8AbBAuANgpWyk0Z2uDHHnOKOOvwIt/ZXD7v/3fNre2++jjr8SHfWqNPcqCPe7bbueM5t2L7R3tu6s0edXvl+d+3fU/mYRfhek/Aee5zGHHOyG3vsKZVt0fepx3HQrv27K4+l3j5jb3/+m6fcMzu3VfaRHIKVou7jJ7q5H51lW2+a/1+LKgdznpZOWmAHXKcbpIPryz9b3PTBpQP3xq5r3/Ix9bH0MfWxfaQw9Zw4xXWfMLHy/TVr644Bt3bbw5WfebsEPE8EKyX6hb9r8s229VY6kGevn5fbL+8loz/tLvnQRbb1Vhte2Ojm/2yRbTVu7kdm24F9rm291Xb7Hi9dd4Vt+UMjp4tHX+S63jve9pL16AuPu/5n1ngb8SIgWCmZduL5btafXGpbb6dnXUUrazqlubHrOtt6O4V02uq/sa3GrbQw6/SyGn1MfWwfXGKhuuRDn7atdClYd//y3spbNIZgpaTa6eBQfVt/5Jb+90rbyoZOa1ZOXlJ5W41GfJc0ORpae+H37b/VXf3oVwt/YOoxGX5Km4X7tq62cN3nTdCLgGClaP6EOe7s955uW9Ut3LTMJuIfsq30aWSlEVaUu56+1931y3tsq3E+ByuvWA3SaHvh5mX29jnbQy0EK0U6GDTBrfmsavTMqgM67V/WqHmrQfr8+jr09TTD12Dp59NorH7x+veyfc9vKqNSzXnpFWDRz1n/GqXH3ecXKLJEsFKmX+ilk75hW9XpGfbqR2+o/NKmQaMqja6i7N6/x8199Cv2dTxne83xNVjXT/hCzcl1PT5rtz1UGQnX8xgpWCf9YUflcdfoWvv10M9f832IR7AyEDcBLzogdFqQNI0gvmUjvKgJcUnitNTHYCkocSEXzTPeZZPjikmz9Hkm23xmtVdRh+u+f4b9F3EIVkbymM+qNYLQAZnExL+PwYp7ZVOS/nlopDX7w5dG/g5oJNezeqZtIQ7ByohGO7ou68jX5zuG07O4Du56TjvqUWtUp88ze/0Xbat1vgWr1ml6mhf3ajXAbPu5KGBDtfKiR0gIVoZ0ehB3GqILLf9+/bxKvFpR64DUs/ksi5UmjZPgW7CiLnSVVi6grZeevOZ+ZNYbo62kRrohIFgZq/WKna6G1itGzdLBUGveKumI+Bas+z51W+VxqibN0dVwGmUl9aQRCoKVg4U2yhpjo60oy7asdPc98yPbalyteas0Tj18C1bc18vEd7ERrBzomXWZnbJFzWeJ5pc0z9SIWlfXp3W6ExeAogUr7nRZ11jNta8XxUWwcqLJ1/lnzLGt6hqdz9KBqPmxqFMdnXpo/WK9H68RPgUrbh6RYBUfwcqRXubuOel826qu3vksRarWFdvNjNjq1S7B0pOEb3eXCA3ByplOTzQ6ilLPnJNecdJ9m6IkfU3RcD4FS4+1HvMouto8jVEokkGwcqYDaGHXl2Lns+IO+lrzVrp53MLNS20rPT4FS+K+3iweLzSPYBVArejoGf/SdVdW3g6lyXstrtYpYTU6BVQwhv9/SYsLgD5/0YKlC3j12EVJe0SK5hGsgoi7mFF00OvgH0qxipq30sWhrS5qrpdvwap1LZws3bLS9TV5aQnSQ7AKQqMkTQbrFDHK0PmsWhP2WV4A6VuwNLrSKKsWTcIv2ryscF9/yAhWgShWcRPCohBJ3CURWS/18C1YUmtEO5TCpcd03a8eTv30GvEIVsHUWrQ8eMBoRFaNTgF1CUOWfAyWHj+dUmu01Qjdv0wT8xq96to2ZItgFVCtW9FE0byV7ss+GLWs+Bgs0Yi21iu0cTTy0sWm+v5+uv2JzB/3EBGsAmr22T+vOPgaLFG09ATR6GNdjUZf+l516qiRLpJHsAoq7orsalpZMN0qn4MleoLQxbfNjGqjaLSltZuKV9G/f58QrAKr5+V30YGRxqLmevkerEF6klC4khhtDaVTx3XbHnJ9z6yphAzNI1gFV+tWvjL0coc8tEuwBmlh+rSTzo+9BVAzFCu92ki4mkewCqzWFfBD5RmGdgvWII209KqtThW1nRTFSlfT65VGNIZgFZQmgzWHpfmVeuggqLZ8JwvtGqyh9PMYe8xrf7orqZGX7saxaPPyXH5mviJYBaRI1brNcTUKgwKRtRCCNZR+PmOOObky56WAtTL60iuLaf5dynZDsAqo1m2O4+QxnxVasIZTsLps3ksjLwWsUUSrfgSrYDRnEnel++D1PTpFiZJ1JEIP1lAafWnSXst+FLB6aTI+y+VUviJYBaIIxa0l1JXs+vNcRx1+ROwV2nqmznI+i2BVp5GXLk1RvOqhJVWDT0iojmAVhJ6ZV05eUnkbRQufB19ZqvUKoiKhWGSBYMXTE5EWW+ttnLyvp/MBwSoIvSKoSdwo1U4ZdBDEPXtnNZ9FsOpT6+cl3KI5HsEqAJ02xF3RrtMEnS4Mp9GYQhf3zJ1FMAhW/WpFS9dncbfTaAQrZxpVKTpRBuetom5loljlPZ9FsOqnJxndPDDq56W7P/CnxqIRrBxpUlZ3ZdAvcZR6Dvi857MIVmPi7hZLsOIRrBwpVieN7HRRGpmD0i1S4q4BauRjNYpgNSZuVK2F0vxtxGgEKydxz7LS6DOtRmkKoEZtUdKKB8FqjH5GOi2M0n3/DPsvqiFYOdCFhXH3ZNe8VTN3DtV8Vtx1XPp4acxnEazGxT1mBCsawcqYoqLTAY2IorRykNe6Ul4fVx8/SXEHnz6XPifeFHdKqBdX9GSF6ghWhhSpG7uutWh1uihJzDVlPZ9FsBoT9yJJo1MBoSFYGdLdLLtPmGhb1SV1pbPCmOV8lk/B0mNz8ehpFvTxtvfaraUHVw9kRaMrjbKqqXaBMN5EsDIS96wqOhWYvX5eYvNLOiB0YETR50lqPsunYFW7cHPttofcws3LbCt9tX4uQ5df4e0IVgbqmbfSley6oj1Jta6gV0gUlFb5Eiw9/vd96jbbejvd4mX+zxZXnjjSos8fNyWgF1t6Vs+0LUQhWCnTL2mtm/HptCStv3iz0EIZd5uTJOazfAlWrdGNRps6Jbvrl/faXrL0e6D7nOlriJLEz6LdEayU6Zc07mZ8Sc1bRdGBomt+opaCSKtRaZdgDdLFm3c9fU/lT3QlQSNszV9GjaxEoyu9OqhoIhrBSlGtSwx0+pHkvFWUWgeqPn8r81m+BEt0nZoCUg+Fa8MLj1fC1czpul70uGT0RTZnNtH24jF3VR+ClRIdFDo4ougZde6jX2nqQGhGmvNZccFKkwKrx+9uO4XT118PjTjn26g37jS5GsVLlxzo82m+65md2yqffyjd510ff8yxJ9vPv7PyRFGPtdsetkn/pbaFWghWCvRLW+tmfHncRiSt+SyFWYHOU6MjlFpLo7JCrBpDsFJQ6xKGvH5JdYqyzOISN5+leRSdqjai2qUCWdPop9ELLjUC0tySHpc85PV74DOClYK4uSudUujUa/jpRFZqrWNs5hVLHfhxc2RZaCZYolFwj/289Jee40KeJE0H3LhpaUMjQryGYKVAp0c6TRpOv6hZzltFiTsdavZ6sFpzZGlr9RR7MFwaKaY54tJpd589IeT1hOU7gpWSaqdJrR5USVJQFdahWv36NHqbbSPLNA/4ahSBZubeouj70NKdrveensioS08A67Y9ZI/tw4SqRQQrRZrLUhR22cjqFy8+VfcrWVkZ+vUpVI3OXUXRaEUfNwtpP6b6PvQn6k8c2elGHXFsJcb6F0WPof4pUvr3c/u5ax/JIFhACxRnRk3ZIVgAvEGwAHiDYAHwBsEC4A2CBcAbBAuANwgWAG8QLADeIFgAvEGwAHiDYAHwBsEC4A2CBcAbBAuANwgWAG8QLADeIFgAvEGwAHiDYAHwBsEC4A2CBcAbBAuANwgWAG8QLADeKGqwOi1Yz9omALzBgvV+C9aAS0BiwZLJ/dPL9gYA3rBu6qrEOpPYB5LuvulPlkturG0CgHNl99C6nlWTXEKSDtYKC9ZltgkArlR2d67tWdXrEpJosCb3Te+1j3iHbQKABavUs7bn+/22mYhEgzW1r/fo3aW9v7VNAHBHlt/xrv6eFS/ZZiISDZbYaWG/nRZeaJsAAmang/fb6eBU20xM4sGawvVYAMxhCV5/NSjxYInNZQ3YR+6wTQABstHVz210Nc42E5VKsLr7Zkwtl8p9tgkgQElPtg9KJVhio6z19tEn2iaAkCR87dVQqQXL5rI6Dzr3pH2GkbYLIARlt+Mw58bZ3NWAS0FqwRIbZfXaZ7jDNgEEIK1TwUGpBku6ufodCEO5tGRdz/evsq3UpB4sIVpAe7NXBRNdghMlk2AJ0QLaU1axksyCJRat+Rat620TQBuwWH3ZYjXfZSTTYIm9ejjJXj3st8880nYB+Oi1VwOn2quB612GMg+WaJH0Hrf3mzbausx2AXjERlV3HuHecVWSi5rrlUuwBmm0dci5XsIFFJ9CNcK5FVmPqobKNViDLFydB13pKhtnTrWvqMPeBaAIyu45y0T/Ya78TQvVgMtZyf4ViuJVdqWpZVceV3au0zk3zjHfBaTP5qXsv0+WnBsouZK9LfcXIVJD/Q4I2D8sxjgWRQAAAABJRU5ErkJggg==',
            'CSV': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsCAYAAAB5fY51AAAZXElEQVR4nO3dDZAcZZ3H8WdANG9nhARCOEnWRAhBSQIIlERJcl5yidxdsgavNpYJuRLv4LSUk2D5esnpeXCXcL6VBuq05E3hSmAjpYiJpQEMnrwYEApFBZKgRxBCCOT9be7/2zA6LNPd07P99PQz/f1UbbZnC3anZ6e/8/TTPb0VVzAze98zxR3mprnqwSl2s8u5in2uvs4BHpx+9KRbLzv740srlcoDdhMFV7GPtpvd29N18LDKh121Oq/qXJcDcjJ55Mnus2d9dO+gV73mLKJVfBX7aBsL1fSDlcqHq646z24CuVOwlk/9tPvDzmefOWbIyBMtWs/bl1FQbQnWvN7Fr9tR2f15W1zsgDaqBUu27Xlh0/DXvHYy0Squ3IM1q3fBvGrFfYN5KRRBfbCEaBVbrsGa9Z0Fn69WqxfbIlAI/YMlz+za8ivbPZxoiyiY3II1c1WPjarYBUSxNAqWbNr++x+N/bPXv8MWUSC5BItYoaiigiVbdm29deSQo+baIgrCe7CIFYosLliyhWgVitdgzeztWWw/QcECCikpWPLQc79eOmnEhM/YItrMW7Bm9/Z0HahU1nM0EEXWTLBk44tPXtr12jErbBFt5C1Ytiv4Y/s03QEF1mywZOueFz5w1KDhX7VFtImXYB0616raa4tAoaUJlvx66xPnTDhq3F22iDbwE6xVPU9UnetyQMGlDdbO/bsO/O7FzTOIVntU7CNTNnc1/UDF/dgWgcJLGyzZtX/3/sGvGnRGhTdL5y7zYM3q7VlVrbi5tggUXivBkt379+gKDxMsWhsccpNpsF56U/NWWwSC0GqwZPOOPzx37NBjxlu0nrebyEGmweK8K4RmIMESopWvTINlu4NX2+7g+bYIBGGgwZJndz23eeTgoyYSLf8yDdbMVQvWO1edYotAELIIljz+wsafjB/e9XZbhEcZB6unap+AYGQVLCFa/mUWrNl9b8VxT9giEIwsgyVEy68sgzXdgvVjWwSCkXWw5DfPP3HNiUeOW+yQuYp9ZIJgIUQ+giVc4cEPgoVS8xUsIVrZI1goNZ/BEqKVLYKFUvMdrB37dh78/fanp/Nm6WwQLJSa72AJV3jIDsFCqeURLCFa2SBYKLW8giVbdm/de8dTPz1x/rhzN9pNtKBiH5kgWAhRnsGSp7Y/vW30sFFdvO+wNQQLpZZ3sIQrPLSOYKHU2hEsUbRspDXCFpECwUKptStYcv8fHvrtW0ZNOsEW0SSChVJrZ7CEN0unQ7BQau0OlhCt5hEslFoRgiW3b1z79TldMy6wRcQgWCi1ogRLiFYygoVSK1KwhDdLxyNYKLWiBUt+9Lt173/H8W/7mi2iH4KFUitisHbs21n9waY7Fsx/4zv/x26iDsFCqRUxWEK0GiNYKLWiBku4wsMrESyUWpGDJZu2/37Xfc88OJErPBxSsY9MECyEqOjBEqL1JxX7yATBQohCCJYoWmOG/flxZb/CA8FCqYUSLHnkud9setOIE8faYmkRLJRaSMGSsl/hgWCh1EILlpQ5WgQLpRZisOR/N/98/VtHn36aLZYKwUKphRosKeObpQkWSi3kYEnZokWwUGqhB0vKFC2ChVLrhGBt37fD3fLb777//JN7Ov4KDwQLpdYJwZKyvFmaYKHUOiVYUoZoESyUWicFSxStz93zxWmXve0THXmFB4KFUuu0YMkTL2w6sH7Lw+M78c3SFfvIBMFCiDoxWKI3S3fiFR4q9pEJgoUQdWqwpBOjVbGPTBAshKiTgyV3/d/Pnvuve64cv6r76o64LA3BQql1erBk3VP3bX7bcWeMtsXgESyUWhmCJZ1yhQeChVIrS7Dklsdvu2f++HPPssVgESyUWpmCJdf+8ts/Of/kv3u7LQaJYKHUyhYsufXxH9wwd/zs99hicAgWSq2MwZKbfvO9L7/7xL/+kC0GhWCh1MoaLF3h4aqHr1+65LQLP2M3g0GwUGplDZaEGC2ChVIrc7BE0Vq96c6eUK7wQLBQaqcceZK74pyltlReerP0fz/0zRkhXOGBYKHUBrlXu1vnXmNL5RZKtCr2kQmChRDt37evL1jDjhhqt8othGgRLJRatVp1y8/+lJtyzJvtFh7Z8uvdn7zrstFFfbM0wULpLRj3t+7vT1lgS5AiX+GBYKH0Jgwe674863JbQk1Ro0WwUHq7d+5wt513A/NY/RQxWgQLpbdv7x730dM/4OaMe4fdQr2ivVmaYAHmja9+vfvqnOW2hP6KFC2CBZhdO7a7b8/9ujt26DF2C/0VJVoECzAH9u93f3HsW90np37EbqGRj9992a2XT/3EXFtsG4IFvOTFbc+73vnX2ijraLuF/vS+w3a/WZpgAS/RWe9njDjF/ceMpXYLjbQ7WgQLqKNR1srZyznzPYai9e/3fenSy87+xAq7mSuCBdQ5eOCAG3bgNe6b865yQ48YYl9BI49t2+BWrL/qvVfOuPybdjM3BAvoZ8+uXe6to05zl8/4F7uFKO14szTBAhrY8cI2d8GUhbzHMEHe0SJYQAO6isOL27a6Zed8zM1+wwz7CqL88Mmf7P3P+1eetab7Ww/YTa8q9pEJgoVOQ7Sap2hdcf9XJtzefeMG51HFPjJBsNCJdEKpjhwumtLjPnja++wriJLHm6UJFpBARw63v7jNnTH61L6JeI4eRrv5sds2X//Qtyf6ihbBAppQ2z0c9qqh7qIz3+fmn3CufRWNfOWhazb98LG1k31Ei2ABKehN0rt37XQnHzPR/cPpi9zU4860r6I/ReuDkxaPtcVMESwgJb2FZ/sL22zUddCNPXKMu+DURe7trz+LXcV+fFzhgWABLdAuoq5UqtGWVCqHuTeNOsmde8JfuROOfIObfPSb7KvlprfwfO7eL2V6hQeCBQyAjiLutN3E/fv22q2XG3Pk8W700FHu9OOm2K1DKpWKO+GocaW5HLOi9a1f3tTz1b9cnslfliZYQAa0m7jLRlyNwlV2gwcN/ue73vu9L9jigBEsIEMK1x7bTdy7d4/dghAsoOB07tae3bvc3j173MGDB+wr5UWwgIBonmvvnt32Uc54ESwgUBp5HTx40JbK4whX+ZvV777pu7Y4YAQLgFeHV92M27tvXOsyQLAAeEWwAASDYAEIBsECEAyCBSAYBAtAMAgWgGAQLADBIFgAgkGwAASDYAEIBsECEAyCBSAYBAtAMAgWgGAQLADBIFgAgkGwAASDYAEIBsECEAyCBSAYBAtAMAgWgGAQLADBIFgAgkGwAASDYAEIBsECEAyCBSAYBAtAMAgWgGAQLADBIFgAgkGwAASDYAEIBsECEAyCBSAYBAtAMAgWgGAQLADBIFgAgkGwAASDYAEIBsECEAyCBSAYBAtAMAgWgGAQLADBIFgAgkGwAASDYAEIBsECEAyCBSAYBAtAMAgWgGAQLADBIFgAgkGwAASDYAEIBsECEAyCBSAYBAtAMAgWgGAQLADBIFgAgkGwAASDYAEIBsECEAyCBSAYBAtAMAgWgGAQLADBIFgAgkGwAASDYAEIBsECEAyCBSAYBAtAMAgWgGAQLADBIFgAgkGwAASDYAEIBsECEAyCBSAYBAtAMAgWgGAQLADBIFgAgkGwAASDYAEIBsECEAyCFaBRQ452owaPtCXnJo882f79kweffcT+de7pXc+6p3c+Y0vFNmnERPv3kPp12b5vp3ts2wa3Y78+b7SvAAQrCGcf+xY3fvhYN8k26PqNuhkK2C/s4+7N9xViw9f9V6TSrovipfuv9fnp5vstaDvsq9nS49w9fk7f/dLPu+5XN/c9bkWk58N7J8x3U0ef0Xdf9bhc/+gtXh6XIiFYBaWNZubx57izR7/FDTtiqH1l4DbbiKv3se+7NU/emesTW/df66IYHGujwyxoA1296Y7M4qVYLTvrElt6uWU/u6Jw0dIIe+X0y/oe13r6/f7T2o9n8ngUFcEqGG3YC086L7MNuxE9oRWu3sdv71v2qXvcHFuf+a/YuLKi+5/Futzyzq81vI/6nu+67QJbKo4lp17kZo05x5ZeafWmO92K9SttqTMRrILQiOqSUy/0Gqr+tDGu+PmVXkYQGgUssfXReuVB66JwXffozXYrHd3X62Z+yZYaK9IoS1G9duYX+z43olHWojUfsqXORLDaTE+8S079x765iHbRrtWK9VfaUjY0v7J86qf71i1vms/Rumi+q1m6nwrW0COG2K1Xuvup+9yye66wpfabdfw0t+S0C22psSLdVx8IVhtpw1565iUtj6o0md7fUNv49H3T0oZ+6bp/6xupDIR+dtpYZbkeNRo5rn7yDltqzkVvXtQ3xxZloY1ainDUVXNX44d3uShp1zs0BKtNkl4p+9thh/nX2aunNu4HtzySuPFoV0wTyZq01y5PMwYaLUUqbnelRvddcy13b77XfuZG+0o0RWvyCDuyqPWxdUkjTWT0GGmUFUVHDK979CZbah89FiunX25LjWldtc6djGC1QZpY6UmojUVzKK2GRD9PE9/aKJMMJFpLz/xI7K6tonvdr25ytzz+fbuVnkI4yw5KaCTUzLrocUsTmRU2MlQYGynC3FDcZLukXd8QEaycKR7NxEob98qHrs10eK+ffdEpi2x3a4jditbKE18jOu0KRtFISnMrCnAWtC5JEb7SHr80cdT3jPvdtHvyPepIZo1GV1k9vkVFsHKkXbRG5/r0p4lTTRy3MspJoie8jt7F7V5pt3PJus/aUvOW2Vxc1PfURnSRh/ODtC4LJ8zvG3E10soG3PvOr0cGXbux7TplICmmes7oBaHTEaycaCSgCVNtZHHSjgpatXDCeX0jlEbSBkvrFjf/c6l9L53o6YvmdrS7pM+i0enyn69saTSUNPmuc7KyDm8zNHrVKDZKu0d/eSFYOVGs4o7uSN5HeKJetdM++d81bo670HY1G9Gu4EVrP2ZL/tU26IHEMSm+eb2g1Eu6TxpFajRZBgQrB3GjmZq8Y1VTf980MmllUlyjm6jJ4Hat10AUbfI9adTXypxjqAiWZ3p11Ogqblcw9Cdc3Abue3fQh6iRZ41GjBo55uVaG13FnavXrt3UdiBYnsWNPkRPfG0AIVOQo3Z3QwyWFGXyPelATZ73pQgIlkcaXcXNPYjmHjQHETKdzFib8O4v1GDF7YZpNLNozYf7PvsWd/RVQn18W0WwPEoaXYW+K1gTt0sY6jomvdjkMTeXdB/0QqcXvDIhWB7FneinCW492fJ4lfYtbhSg9ctrNJK1uJGj3hGgc8t8ijv6Ku04YtluBMuTMs091B9pbCTrq0Hkpd2T73GT7Z30gpcGwfIkaXfQ95M9T0m7LrLuqXvdFeuvCmoD0+hY6xU1+a7rb618+Fpbyp7OKdPJolE66QUvDYLlSdyrYyfOPcTtPtXoHCad56VLNIci7oVH8dUpBT7E/VzppBe8NAiWB0kjDp+vzO2StAtcT+HSY6BwaaMvMkVYMY7iY/JdIzvNf0ZRqBSsMiJYHiRtvGnf+hKKuMn3KNpV1Bt3s/pjEj4oWApXIzqlQKcWZClpst1HJENBsDxImoTWbkRRN86B0MhAI8uoOZ8kOvKmeCnmGkUURdLku3bvtZuflbgTccs62V5DsDyIm3/QE677tvfZUmdStDRZHDUiaZY2SMVLI5h2j760TnEhzvJcMz1uGtFFKetkew3B8iDuRMq0l24JkTbwZWd+JPIxaEW7dx3jXoQ0J5fVG6Ljfo5odJXlaC40BMuDsgerRrtSzVzhNC2d16V4adcxL0kjnyzmJRX6uGvil+m5E4VgeRA3B6ENrQxXhqzRxqc/pvqu8XMyD5dGNnmeJqFgKVyNZPF7VeDj5srKPNleQ7A8WD33Bvu3sSznO0KicOnoqd5QHLXRt0qT9TqT3vdEfVJQBnowRXN/OmG0kU6f+2wWwfLA9ytx6HSe2lSL18wx0yIfp1ZotNXKX35ulqIbN/k+kPf26THR946i89Y67dy9VhAsD5jDap42VP3dQZ2/pdFFVAyapdFWq3+mrBlxk+LaRW118j3ucjZS9sn2GoLlAcFqnaI16aWAtTr68hkt3SeNoKPoJFKdipGWzmzXCK4RnjN/QrA8iDvjWxuR5jqQrDb60ogm6gUgis/zlRQshauRVn5u0txYFkcgOwXB8qCsZ7r7pHhpw05ztLHV0U4S3Y+4wKT9/ca9wGk3ULuDOIRgeZD0hPa1IZWF3mu38KTzEsM1kDmlONp10wR51M9Pc/qBQqzvFaWsR5WjECwPtLug3YYoHPEZOG3oGpnosY7ja3cqbvJdc2jNXo00aTSu0ZVGWTiEYHkS91dXfL3yl41GOjp3KS5arcwpNUM/M+5FSZd/aea8sLjrpnEKzCsRLE/06h81LyHNPqERT9HSOws04mpEc0maU/JBwVK4GmkmlDqRtoyXIRoIguVJ0jxWM09oNCfpsfa1WxX3c5sJZdxupe6v7jdejmB5old+nVsTRU/oUP+aTBHFvR3qUk8HOfQ71oR51K5/3OS7/t+458dAzprvZATLo7hXUGHyPTtxJ+v6CpbE/Y71M/WzG9GRzririmp0xovZKxEsjzS/oXmOOBr2a/iPgYkLls/RStLvOOr3GzfZznRBNILlWdyGJGkOgSNa3OOsUY5GO74oWApXI41G0fpv9f9E8X1/Q0awPEt6ckqjJzXS0ZHCqGuQ+Q5A3O6dduu0e1cvbjdSozGNytAYwcpB0jvxJW6C1ie92XjeuNlu6ugz+jbq6x+9ue9zWpNGTOz7XqufvLNvo8uTTmnQ5HcUBUPh8CVpAr3+9AT9t3FXFW3X8yAUBCsHenJqg4o6mlST95M16rB8mhGJ1m351E+9bHST93rEnS2uc910zptvcaOm+hNAox5z0UX6NLryGdfQEaycaPShs7KT5LWxx2049RtYkqhY3PLYbe7Kh6+zJb+SRld57W4n/X4VIo0843ZdmWxPRrByFLVx96c/sqCN3dcrra6xrj8OESXN9ZfiJrt1QMHnpYsbje76q4UiDwqnAtqI3sR89+Z7LViX263GNBL09Vh1CoKVs7hdh3p6v+EVtrE3u2vWDG1MS069sG80ECfNK31csGp8BLiZWKVZjyzETb7r9/lTG7lGzWUqVAoW4hGsNmg2WqJgtToRXqONu9sm1nVJliSaR7nQNpxmRyVJ75msp3DpmuvNfu8oM48/p2+EqPWKknY9sqD7Ezf5rmDrv2kkr6mA0BGsNmnmyGE9vUKvsQ1eR5v0atwMvblWMZk1ZprdSqaNfMm6zzT9/UWnbShaGr01S7uKGv38Yssjttzcz9L315FIRTfqhMt6Pk8WjZPmxahGj7t2XRU0xCNYbaSJb40Uko4e9qcntjZ0jR70UU8btj6Sdvv600aTNlY1GjVowlnxSituXfS4jB/e1bc+zUSqRjHMc1ewnh53PRZp5HVgoBMQrDbTRq5XZX1uFwVDpzIoHgPR7EEFn9oZq5q4yfdGNLrqH2s0RrAKQht7muuVZ0VHr7K8BK/Cq93dpIl4H9q1G9ifRs5Rp4z0l+aILAhWoWjXauGE+TYHMs17uLShaDdEoysftNFqtJVmpNEq3+vSCp2+oHgn0ZHBIt3voiNYBaRwzbIjYVn/ZWTRLtMaOxo1kKOOaWhOR+d9afI/awqVjjrmtS5pKNQ6GBH3++PIYHoEq+D0xNefddcGrwnotCMvTaZrg9bGrff5DXSeqlWKsI5aaldx8siJfevVCq2HzsRfZ0dLiz7vo3VWrPvv6ut+64Ra/V6QDsEKjF6xtSHoryPXaOMfZhtE/a6FThlQnOq/ViRaB63LqMGHjmrWKGbbLbL197vo69KM2voSqYEhWACCQbAABINgAQgGwQIQDIIFIBgEC0AwCBaAYBAsAMEgWACCQbAABINgAQgGwQIQDIIFIBgEC0AwCBaAYBAsAMEgWACCQbAABINgAQgGwQIQDIIFIBgEC0AwCBaAYBAsAMEgWACCUdRgdVmwnrBFAPgjC9YbLFgbXAYyC5bMXNVTtU8A8Edr5t2YWWcy+0Yyq7fngWrFTbZFAHCu6u5Y033jdJeRrIN1tQXrfFsEAFepumtWd9+42GUk02DN7O1ZbN/xG7YIABasSvfq7htW2WImMg3WvN7Fr9tR2b3VFgHADa0OOnJV99XP22ImMg2W2G7hKtstnGuLAErMdge/Y7uD82wxM5kHazbnYwEwh2d4/lVN5sESm8vaYN95rC0CKCEbXT1oo6sptpgpL8Ga1btgXrVS7bVFACWU9WR7jZdgiY2y1tp3n2aLAMok43Ov6nkLls1ldR1w7gH7CcPtJoAyqLpthzs3xeauNjgPvAVLbJS12H7CN2wRQAn42hWs8RosmcXZ70A5VCtfXNN9w8W25I33YAnRAjqbHRXM9C04UXIJlhAtoDPlFSvJLVhi0Vpm0VpqiwA6gMXqXy1Wy1xOcg2W2NHD6Xb0cJX95OF2E0CIDh0NnGdHA9e6HOUeLNGbpHe63V+w0db5dhNAQGxUdc0QN+jiLN/U3Ky2BKtGo62Dzi0mXEDxKVSHOXd13qOqem0NVo2Fq+uAq1xs48x5do/G2pcAFEHVbbRMrDrcVb9godrg2qxiH4WieFVdZV7VVadUnetyzk1xzHcB/tm8lP37QMW5DRVXsc/VVUWIVL3/B+VPYSz5FmyjAAAAAElFTkSuQmCC',
            'TXT': 'data:image/png;base64,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',
            'ZIP': 'data:image/png;base64,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',
            'PPT': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsCAYAAAB5fY51AAATzUlEQVR4nO3dC5BV9X3A8f/lsbwWWDAICgiI2qhx3KZ1tDFGAxU11WETGKcxMxWtRseMEY0S21RUYtv4xkSttFgwM5pJQYMNTXwEsSFOtU3N6kxjmqoQxLcV2F0ee/dx8vtdWbNZ957de/d/Hr/7/35mrvs/68zu3nM9X8859/zPLbic2XH6kY1Rd9epUeQaC4Vodumrcw3yrwDvRh75idZxCz5/8fhFF35fFpFz0oLsvXnmx2d3FItXOBc1yR802wEpGXX8Se6gFauivU+s/yLRyr+CPDLz2p8ecZqLuq9wUdQki0DqNFhT7njIdWz7dbHr+S1HjVl00W/k28ipgjxSt7OpsaG1peXOgouWOCBDPcFSHdtf2df186ePJlr5VZBHqrbPn9ske1Vr5Bc3yCKQqd7BUkQr3wrySM32eYfrXtVSGQK50DdYqvjSC22jjmkcL0PkTGrBkljJXhWHgMiX/oKl9v98y8tjTvjMkTJEjqQSLGKFvCoXLEW08ifxYBEr5FlcsBTRypdEg7Vj3twlketeI0MglwYKlmpdt/rRCede3CRDZCyxYH1wMWj7L+QXNMgikEuDCZba+9i6+8edde5FMkSGpCfJeG3+nM0ucqc5IMcGGyxFtLKXSLD0WqtC1P0DGQK5Vkmw1K7br71+0tU3r5AhMpBMsObN2So/eLYDcq7SYHW3tTLvMEMFeXhVmh/Y3bVZhkDuVRosRbSy4z9Y8+dskHNXC2UI5F41wVIarbYH7zp74qXX/UgWkRKvwdJJzW0tu3fKEDCh2mCp4qv/29XxyOpP1l9z24uyiBR4DRbXXcGaoQRLMVk6XQV5eLN9/py1hcidL0PAhKEGSxGt9BTk4Y2cv/qFi1yjDAETfARL7f3pj98v3vnXcydtaN4li0iI32DNmxPJF8AMX8FS+57dvH3sn8ybJUMkxFuwdCpOZ7F9qwwBM3wGSzFZOlnegsX1V7DId7DUnh+va67/3Ll/KEN4RrAQtCSCpVoeWPmziUuuPEWG8IhgIWhJBUsxWdo/goWgJRksRbT8IlgIWtLB6m5rcS2r/o47PHhCsBC0pIOlNFqt6/754oYLr1wtixgCgoWgpREspZOlucPD0BEsBC2tYKnOt9/obr3rG6dN/vu1W2QRVSBYCFqawVJ6h4c9q7/1WaJVHYKFoKUdLNWx7dfFrue3HMVk6coV5OEFwYJFWQRL6R0e9lyx+FAmS1eGYCFoWQVLcYeHyhEsBC3LYCmN1rhTP3eQDDEIBAtByzpYijs8DB7BQtDyECzFZOnBIVgIWl6CpYjWwAgWgpanYKnWdasfnXDuxU0yRD8IFoKWt2Cp3fffdnPDRddcK0P0QbAQtDwGSydLc4eH/hEsBC2PwVJEq38EC0HLa7AUd3j4KIKFoOU5WIrJ0r+PYCFoeQ+WIlq/Q7AQNAvBUhqtjkdWf7L+mttelMVgESwEzUqw1P7/3rJ7/01fnR3yZGmChaBZCpbSydIh3+GBYCFo1oKlQo4WwULQLAZL7du88ZWx8845QoZBIVgImtVgqRAnSxMsBM1ysFRo0SJYCJr1YKmQokWwELRaCFZI8w4JFoJWC8FSoUSLYCFotRIsFUK0CBaCVkvBUhqt96/9y3Om3LN+oyzWHIKFoNVasJTOO6zVydIEC0GrxWCpWo0WwULQajVYqhajRbAQtFoOltq7+YdtxW8vn1kr8w4JFoJW68FStTRZmmAhaCEES9VKtAgWghZKsNTutXe+3nDBVTNkaBbBQtBCCpayPu+QYCFooQVLWY4WwULQQgyW2vWdG5+a9NUb5svQFIKFoIUarNIUnlu/vmrKivsulUUzCBaCFmqwlEbL2mRpgoWghRwsZS1aBAtBG3nciW7qyu/JKFzFl3/pdv3tlV+ZuvaJe2Ux1wgWghaNqnczfxT0hymXWJl3SLAQtK6O7lKwhtVPkKWwWYgWwULQOvd3umn3rnOjjz9RltD+4n91vnf1l06Y8eT/Ncti7hAsBK27s9tNWLLUNVywVJag9jy5odh6x1/9wSGP/WqbyxmChaBFkZx4P+YEN+3u78sSeuR1sjTBQvA69nS4WT/5H85j9ZHHaBEsBK9jb4ebcv2drv7MxbKE3nbefeP2wqYNx+clWgQLwevuitzIY09wh9zzL7KEvnavvuX5hou//kcyzBzBAkT77nY3a+N/uhHTpssS+srLHR4IFiC62rvcuLMWu49943ZZQn/eWXb+v0699bsLZZgZggWIKHKu2MJeVhydd/jeisszjRbBAg7Qi0jHnnKGO/hb/yRL6I9GK8vJ0gQL6EXPZU1f/Ygb3ciV7+VotN6/aek1U25ec5sspopgAb3o3MLCpEPcjAcf57qsGFnd4YFgAX107ut0Y05Z4KbevFqWUI5Gq2Xl9edMuWf9RllMBcEC+lFsKbrJl13NHMMBpH2HB4IF9EMvJu1oLbqpN93l6s9aLN9BOTpZetety05M4w4PBAsog2gNXlrRIlhAjJ5oTVpyiZt8+XL5DsrZu/mHbcVvL5+Z5LxDggUMoCdaY086uXQinncPy2tdf/9bXQ/efXRS0SJYwCBEkStFa9j4iXIy/mtuwuIL5bvoT5J3eCBYwCBFkXNd+ztLj1HHHucmX3K1G/vp0+XfoC+N1uTLb5glQ68IFlAhvbi0c09HqWB1s2a7SV++yo09ZQGHin0kcYcHggVUIYr0AtMO193eJUuiUHCjP3GcG3/2ua7uyGOZ2iN0Co/vydIECxgC/RALvTI+kq99jTxslhtx6Aw39o8/JUs9Cq7uqGOC2RvTaLU+8J0/n7Z6o5eb5hMswIO4cIVu+OiRV875j+0rZThkBAvwSMOlNwPsLh44VATBAvIu6o5K4erScMk4ZAQLMEQvPNU9rlDjRbAAo3TPSx9BGTb8nFlPv7pRRkNGsAAka9jwz878yctPOw8IFoBkESwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmEKzkjDr+JFf/hSVuzKcXyJJfxZd/6aK2Ftf59g6375knXfsLz5WWk1T/hQtc/aIL3IhpM2TJn275uzvk+ajSc3nxuQ+XfZjwF1eU/u5h9RNkKX/0+bc9vMa1fPcuWUIsgpWMsWcsdpOX3SKj9Oz72ROu7ZG1Eq9nZcmvSfJcxslzSkvnWzvc3scfdq2PrBlSiCd+5To3XkJrgQar5QGiFYtg+TfyiGPc1FUbZZSN9uZn3a5/uMnbXoruKU654yEZpa9n76PacB36aHNu96z60uf6xsJGGaEsguXfpGW3yt7IIhllR//j33XvTbKXsl6WhiYPz0cPgd/92nkVR2vaQ1vciKnTZWTDjvmHyz9RFsHy72DZu6qTvaw80EMMPdQYiil3fE/2sk6UUbY0whqtSvYcJ5x/RekclgW6F7n7nm/KCGURLP9mbHpV/pkfu+79Zumwqlp5CZbSaL35pc9UtKel0dJzinnd0+p8+/XSnnCrvEaVPK8gESz/4oL17lXnDemkuJ4fGzZughvVeKIbffICVzf3aPnuwIbye+OCNZSfW5BzS3Vzjyl9HXPy6fKcThpUVPTw8J1LzpZRepJaB6gQwfIvyWD1pSfE9S173eDjVLNn0iPNjVX3hBou+5sBT5TrYa4e7qYlzXWAGATLvzSD1UOj1XDZdTIqr9pDw7Q3Vt3j+tiKVWV/pxpKgKuR9jpAGQTLvyyCpXTvJO7aL7226S3ZyCuV1cZ68D/+m4s75E1zLyurdYA+CJZ/WQVL6QnmuHfF3r9lWekEbyWy2lh1T2uqRKvceS3dy0rruqWs1gH6IFj+ZRmsgTbyPY8/7Hbeco2MBi/LjXWgvca35eR7JZc5VCvLdYBeCJZ/WQZLxZ3PquawMOuNNe7iz2rPy1Uq63WAAwiWf1kHa/i0Ge6QB38qo/7pyeouCddgZb2xxs0H1MnS/7/8EhklK+t1gAMIln9ZB0vFnbCu9G/IemPV680+tuI+GX2U3qXi3au+KKNkZb0OcADB8i8PwfK5gfn8WdXQa83KTb5O6yLSrNcBDiBY/uU9WJW+Uxj3s9J4PvpGwvRHm2XUvzQmDGe9DnAAwfIv78F6b/mlbv8zT8hocOJ+VhrPZ6BzcgQrIATLv7wHq9K/wefPqkbcISHnsAJDsPzLQ7DiblxX6d+Q9cYad5kGwQoMwfIv62DFvaumKj2EynpjPWjFqrKTu6u5ELYaWa8DHECw/Ms6WFNuf6h0q5b+VLNHkuXGOtD5q0rPx1Ury3WAXgiWf1kGK+58j6rmyvAsN9aDbrwv9lOHXl/YmModG7JcB+iFYPmXVbD07X/98Iu4j+Gq9Cp3ldXGOtA8wmr2FquV1TpAHwTLvyyCpbHSQ8G4e8lXe74ni411oFippH53f7JYB+gHwfIv7WDpbZP10Cluz0pVs3el0t5Y9fY4epucOHof9LfOO0VG6Uh7HaAMguVfWsHSUOknSw/mA06H8oksaWysuoc45lOnl0I1UHiVr987WGmsAwwCwfIvLlh7Hl8vezmvy6h6w6dNl43npEFt2Kr4ykvunS//mYyqE7ex+ng++rPLvavZnzTvNNojbh0QrBQRLP/igpU2vSunHgoO5Z20uI01bWndTqavuHVAsFJEsPzLS7B0z0rfRRtKrFTcxpqmat808CFuHRCsFBEs//IQLJ8bd9zGmpZqrh/zKW4dEKwUESz/sgyWXpuk53d8bkBxG2vSNLx6zqqadzd9ilsHBCtFBMu/tIOlb/G3Nz/r9srGncSGE7exJkEPZfV56B5V1qHqEbcOCFaKCJZ/ccHSjXEo55T0JHrHKx98SkynvDun46Q/NabcxvrB3/KSjKpXlL+/Z30UX/4gVD3LeVJuHSiClSKC5V9csCz+x11uY9XDTz2pH4Jy60BZfE3NIlj+EazaU24dKIuvqVkEyz+CVXvKrQNl8TU1i2D5R7BqT7l1oCy+pmYRLP8IVu0ptw6UxdfULILlH8GqPeXWgbL4mppFsPwjWLWn3DpQFl9TswiWfwSr9pRbB8ria2oWwfKPYNWecutAWXxNzSJY/hGs2lNuHSiLr6lZBMs/glV7yq0DZfE1NYtg+Uewak+5daAsvqZmESz/CFbtKbcOlMXX1CyC5R/Bqj3l1oGy+JqaRbD8I1i1p9w6UBZfU7MIln8Eq/aUWwfK4mtqFsHyj2DVnnLrQFl8Tc0iWP4RrNpTbh0oi6+pWQTLP4JVe8qtA2XxNTWLYPk37aEtbsTU6TL6KP1Q07x8sMJgldtYs/pQ0ywctGKVG3Py6TL6KIKVIoLl36Rlt7pxZyyS0e/TD214Y2GjjGwpt7Fm/VmBaapfdIFruOw6GX3U6/Ka5vGDM2oSwfKvUD+htFdSN/doWfqd95Zf6vY/84SMbBk+bYabumqjGybPq4d++o8eDoa0ofYXbv3MRP0cSKSEYCVn7BmL3cgjji5t1Hr4lPTHcSVJIzxe9jL0qx7S6gec6vMKzeiTF7hRjR8cHrc3P2fyf0CmESwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZhAsAGYQLABmECwAZuQxWG+e+fHZncX2rTIEgA+NqBs155DHfrXNeeAtWOq1eXMi+QIAH5r51FZvnfH2g5QEq1m+HC8PAFD/LsE6zXniNVjb589ZW4jc+TIEABcV3AOHbdq6xHniNVg75s1dErnuNTIEAAnWsM8ftumVDTL0wmuwdjY1NrS17N4pQwBw9RMmTpq0oXmXDL3wGiz12vw5G1zkFsoQQMgK7tGZm7Y2ycgb/8HieiwAyuP1Vz28B0vJu4XbnHOz5AEgTC/Iu4ON8tWrRIK1ff7cpkLU/QMZAgiQ75PtPRIJlpK9rKedc6fKA0BYvF571VtiwdKpOh3F9mb5BRNlEUAAIud2j6wb1ehrKk5f0pPkcF0WEJakDgV7JBosxdXvQCAKhbtmbnp1qYwSk3iwFNECapvvKTjlpBIsRbSA2pRWrFRqwVKvzTv8Bnl61zsANaJw48ynXr3BpSTVYCm9Ej7q7togv5h3DwGjInk3sDBseJPvK9kHIt1In06Sbm3dvZJDRMAePQQcP37iUp+Tmgcrk2D1KO1tRV1LCBeQfxqqQmH42rT3qnrLNFg99CLTzo7iUhdFTbI4Sx4A8uE3rlDYMGJk3cqkLgatRC6C1ZvGq6tYbOouRI2y5zU7cq5R/kjOdwEJk21tt2xrzVHBbRsWFZqH19VtyEOkevstE9hpHbN7fJMAAAAASUVORK5CYII=',
            'PDF': 'data:image/png;base64,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',
            'Default': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAB6klEQVR4nO2bT2oCMRSHX/LG5x8UQZispVfwBD1LD2ErbW1Lj9GzdKuiiIgLF17AjRsRHXEmXZSWumkxnfGped8+4ZcvLzNJIACCIAgeozLqN+j3+9elUinIqP89ENGuVqtZo9GYHdo2i4DU7XYf6vX6fbFYBKWycrzPcrl8BYBHALCHtEtbAHU6nXYYhi2tdcpd/06SJAo+K5pNAPV6vbYxpoWIEATB0Wb/P6QlgAaDwVMYhneFQgGSJAFETKnrbElDAA2Hw2djzG0+nwcigiiKQCnlRQXQaDTaGzwRwXa7TSXcMfiPABqPxy/GmObX4HO5HCDiWcz8F64CaDKZvNRqtWYcx7BeryGKIkBE0FpfvAA1nU5vqtVqc7fbQRAE34O21oK1Fogo9aBZ4SSgUqlclcvlvQ/dOc36T5yWgNb6bH5zf3Hc7doJ4r0A5yVwamve9ezhfQWIAO4A3IgA7gDciADuANw4H4dPbR/givcVIAK4A3AjArgDcOO9gIs5DrvifQWIAO4A3IgA7gDceC/gYk6DcivsiAjgDsCNCOAOwI0I4A7AjfcC5D6AOwA3IoA7ADcigDsANyLAoY1VSh30KuMYaK0tHPhaBMBtH2Dn8/nbYrF4j+P4JDYDiGg3m80MHAQIgiB4zQe8XGReoxLRtQAAAABJRU5ErkJggg=='
        }
    </script>
    <script>
        /**
         * lunr - http://lunrjs.com - A bit like Solr, but much smaller and not as bright - 0.7.0
         * Copyright (C) 2016 Oliver Nightingale
         * MIT Licensed
         * @license
         */
        !function () { var t = function (e) { var n = new t.Index; return n.pipeline.add(t.trimmer, t.stopWordFilter, t.stemmer), e && e.call(n, n), n }; t.version = "0.7.0", t.utils = {}, t.utils.warn = function (t) { return function (e) { t.console && console.warn && console.warn(e) } }(this), t.utils.asString = function (t) { return void 0 === t || null === t ? "" : t.toString() }, t.EventEmitter = function () { this.events = {} }, t.EventEmitter.prototype.addListener = function () { var t = Array.prototype.slice.call(arguments), e = t.pop(), n = t; if ("function" != typeof e) throw new TypeError("last argument must be a function"); n.forEach(function (t) { this.hasHandler(t) || (this.events[t] = []), this.events[t].push(e) }, this) }, t.EventEmitter.prototype.removeListener = function (t, e) { if (this.hasHandler(t)) { var n = this.events[t].indexOf(e); this.events[t].splice(n, 1), this.events[t].length || delete this.events[t] } }, t.EventEmitter.prototype.emit = function (t) { if (this.hasHandler(t)) { var e = Array.prototype.slice.call(arguments, 1); this.events[t].forEach(function (t) { t.apply(void 0, e) }) } }, t.EventEmitter.prototype.hasHandler = function (t) { return t in this.events }, t.tokenizer = function (e) { return arguments.length && null != e && void 0 != e ? Array.isArray(e) ? e.map(function (e) { return t.utils.asString(e).toLowerCase() }) : e.toString().trim().toLowerCase().split(t.tokenizer.seperator) : [] }, t.tokenizer.seperator = /[\s\-]+/, t.tokenizer.load = function (t) { var e = this.registeredFunctions[t]; if (!e) throw new Error("Cannot load un-registered function: " + t); return e }, t.tokenizer.label = "default", t.tokenizer.registeredFunctions = { "default": t.tokenizer }, t.tokenizer.registerFunction = function (e, n) { n in this.registeredFunctions && t.utils.warn("Overwriting existing tokenizer: " + n), e.label = n, this.registeredFunctions[n] = e }, t.Pipeline = function () { this._stack = [] }, t.Pipeline.registeredFunctions = {}, t.Pipeline.registerFunction = function (e, n) { n in this.registeredFunctions && t.utils.warn("Overwriting existing registered function: " + n), e.label = n, t.Pipeline.registeredFunctions[e.label] = e }, t.Pipeline.warnIfFunctionNotRegistered = function (e) { var n = e.label && e.label in this.registeredFunctions; n || t.utils.warn("Function is not registered with pipeline. This may cause problems when serialising the index.\n", e) }, t.Pipeline.load = function (e) { var n = new t.Pipeline; return e.forEach(function (e) { var i = t.Pipeline.registeredFunctions[e]; if (!i) throw new Error("Cannot load un-registered function: " + e); n.add(i) }), n }, t.Pipeline.prototype.add = function () { var e = Array.prototype.slice.call(arguments); e.forEach(function (e) { t.Pipeline.warnIfFunctionNotRegistered(e), this._stack.push(e) }, this) }, t.Pipeline.prototype.after = function (e, n) { t.Pipeline.warnIfFunctionNotRegistered(n); var i = this._stack.indexOf(e); if (-1 == i) throw new Error("Cannot find existingFn"); i += 1, this._stack.splice(i, 0, n) }, t.Pipeline.prototype.before = function (e, n) { t.Pipeline.warnIfFunctionNotRegistered(n); var i = this._stack.indexOf(e); if (-1 == i) throw new Error("Cannot find existingFn"); this._stack.splice(i, 0, n) }, t.Pipeline.prototype.remove = function (t) { var e = this._stack.indexOf(t); -1 != e && this._stack.splice(e, 1) }, t.Pipeline.prototype.run = function (t) { for (var e = [], n = t.length, i = this._stack.length, r = 0; n > r; r++) { for (var o = t[r], s = 0; i > s && (o = this._stack[s](o, r, t), void 0 !== o && "" !== o); s++); void 0 !== o && "" !== o && e.push(o) } return e }, t.Pipeline.prototype.reset = function () { this._stack = [] }, t.Pipeline.prototype.toJSON = function () { return this._stack.map(function (e) { return t.Pipeline.warnIfFunctionNotRegistered(e), e.label }) }, t.Vector = function () { this._magnitude = null, this.list = void 0, this.length = 0 }, t.Vector.Node = function (t, e, n) { this.idx = t, this.val = e, this.next = n }, t.Vector.prototype.insert = function (e, n) { this._magnitude = void 0; var i = this.list; if (!i) return this.list = new t.Vector.Node(e, n, i), this.length++; if (e < i.idx) return this.list = new t.Vector.Node(e, n, i), this.length++; for (var r = i, o = i.next; void 0 != o;) { if (e < o.idx) return r.next = new t.Vector.Node(e, n, o), this.length++; r = o, o = o.next } return r.next = new t.Vector.Node(e, n, o), this.length++ }, t.Vector.prototype.magnitude = function () { if (this._magnitude) return this._magnitude; for (var t, e = this.list, n = 0; e;)t = e.val, n += t * t, e = e.next; return this._magnitude = Math.sqrt(n) }, t.Vector.prototype.dot = function (t) { for (var e = this.list, n = t.list, i = 0; e && n;)e.idx < n.idx ? e = e.next : e.idx > n.idx ? n = n.next : (i += e.val * n.val, e = e.next, n = n.next); return i }, t.Vector.prototype.similarity = function (t) { return this.dot(t) / (this.magnitude() * t.magnitude()) }, t.SortedSet = function () { this.length = 0, this.elements = [] }, t.SortedSet.load = function (t) { var e = new this; return e.elements = t, e.length = t.length, e }, t.SortedSet.prototype.add = function () { var t, e; for (t = 0; t < arguments.length; t++)e = arguments[t], ~this.indexOf(e) || this.elements.splice(this.locationFor(e), 0, e); this.length = this.elements.length }, t.SortedSet.prototype.toArray = function () { return this.elements.slice() }, t.SortedSet.prototype.map = function (t, e) { return this.elements.map(t, e) }, t.SortedSet.prototype.forEach = function (t, e) { return this.elements.forEach(t, e) }, t.SortedSet.prototype.indexOf = function (t) { for (var e = 0, n = this.elements.length, i = n - e, r = e + Math.floor(i / 2), o = this.elements[r]; i > 1;) { if (o === t) return r; t > o && (e = r), o > t && (n = r), i = n - e, r = e + Math.floor(i / 2), o = this.elements[r] } return o === t ? r : -1 }, t.SortedSet.prototype.locationFor = function (t) { for (var e = 0, n = this.elements.length, i = n - e, r = e + Math.floor(i / 2), o = this.elements[r]; i > 1;)t > o && (e = r), o > t && (n = r), i = n - e, r = e + Math.floor(i / 2), o = this.elements[r]; return o > t ? r : t > o ? r + 1 : void 0 }, t.SortedSet.prototype.intersect = function (e) { for (var n = new t.SortedSet, i = 0, r = 0, o = this.length, s = e.length, a = this.elements, h = e.elements; ;) { if (i > o - 1 || r > s - 1) break; a[i] !== h[r] ? a[i] < h[r] ? i++ : a[i] > h[r] && r++ : (n.add(a[i]), i++, r++) } return n }, t.SortedSet.prototype.clone = function () { var e = new t.SortedSet; return e.elements = this.toArray(), e.length = e.elements.length, e }, t.SortedSet.prototype.union = function (t) { var e, n, i; this.length >= t.length ? (e = this, n = t) : (e = t, n = this), i = e.clone(); for (var r = 0, o = n.toArray(); r < o.length; r++)i.add(o[r]); return i }, t.SortedSet.prototype.toJSON = function () { return this.toArray() }, t.Index = function () { this._fields = [], this._ref = "id", this.pipeline = new t.Pipeline, this.documentStore = new t.Store, this.tokenStore = new t.TokenStore, this.corpusTokens = new t.SortedSet, this.eventEmitter = new t.EventEmitter, this.tokenizerFn = t.tokenizer, this._idfCache = {}, this.on("add", "remove", "update", function () { this._idfCache = {} }.bind(this)) }, t.Index.prototype.on = function () { var t = Array.prototype.slice.call(arguments); return this.eventEmitter.addListener.apply(this.eventEmitter, t) }, t.Index.prototype.off = function (t, e) { return this.eventEmitter.removeListener(t, e) }, t.Index.load = function (e) { e.version !== t.version && t.utils.warn("version mismatch: current " + t.version + " importing " + e.version); var n = new this; return n._fields = e.fields, n._ref = e.ref, n.tokenizer = t.tokenizer.load(e.tokenizer), n.documentStore = t.Store.load(e.documentStore), n.tokenStore = t.TokenStore.load(e.tokenStore), n.corpusTokens = t.SortedSet.load(e.corpusTokens), n.pipeline = t.Pipeline.load(e.pipeline), n }, t.Index.prototype.field = function (t, e) { var e = e || {}, n = { name: t, boost: e.boost || 1 }; return this._fields.push(n), this }, t.Index.prototype.ref = function (t) { return this._ref = t, this }, t.Index.prototype.tokenizer = function (e) { var n = e.label && e.label in t.tokenizer.registeredFunctions; return n || t.utils.warn("Function is not a registered tokenizer. This may cause problems when serialising the index"), this.tokenizerFn = e, this }, t.Index.prototype.add = function (e, n) { var i = {}, r = new t.SortedSet, o = e[this._ref], n = void 0 === n ? !0 : n; this._fields.forEach(function (t) { var n = this.pipeline.run(this.tokenizerFn(e[t.name])); i[t.name] = n; for (var o = 0; o < n.length; o++) { var s = n[o]; r.add(s), this.corpusTokens.add(s) } }, this), this.documentStore.set(o, r); for (var s = 0; s < r.length; s++) { for (var a = r.elements[s], h = 0, u = 0; u < this._fields.length; u++) { var l = this._fields[u], c = i[l.name], f = c.length; if (f) { for (var d = 0, p = 0; f > p; p++)c[p] === a && d++; h += d / f * l.boost } } this.tokenStore.add(a, { ref: o, tf: h }) } n && this.eventEmitter.emit("add", e, this) }, t.Index.prototype.remove = function (t, e) { var n = t[this._ref], e = void 0 === e ? !0 : e; if (this.documentStore.has(n)) { var i = this.documentStore.get(n); this.documentStore.remove(n), i.forEach(function (t) { this.tokenStore.remove(t, n) }, this), e && this.eventEmitter.emit("remove", t, this) } }, t.Index.prototype.update = function (t, e) { var e = void 0 === e ? !0 : e; this.remove(t, !1), this.add(t, !1), e && this.eventEmitter.emit("update", t, this) }, t.Index.prototype.idf = function (t) { var e = "@" + t; if (Object.prototype.hasOwnProperty.call(this._idfCache, e)) return this._idfCache[e]; var n = this.tokenStore.count(t), i = 1; return n > 0 && (i = 1 + Math.log(this.documentStore.length / n)), this._idfCache[e] = i }, t.Index.prototype.search = function (e) { var n = this.pipeline.run(this.tokenizerFn(e)), i = new t.Vector, r = [], o = this._fields.reduce(function (t, e) { return t + e.boost }, 0), s = n.some(function (t) { return this.tokenStore.has(t) }, this); if (!s) return []; n.forEach(function (e, n, s) { var a = 1 / s.length * this._fields.length * o, h = this, u = this.tokenStore.expand(e).reduce(function (n, r) { var o = h.corpusTokens.indexOf(r), s = h.idf(r), u = 1, l = new t.SortedSet; if (r !== e) { var c = Math.max(3, r.length - e.length); u = 1 / Math.log(c) } o > -1 && i.insert(o, a * s * u); for (var f = h.tokenStore.get(r), d = Object.keys(f), p = d.length, v = 0; p > v; v++)l.add(f[d[v]].ref); return n.union(l) }, new t.SortedSet); r.push(u) }, this); var a = r.reduce(function (t, e) { return t.intersect(e) }); return a.map(function (t) { return { ref: t, score: i.similarity(this.documentVector(t)) } }, this).sort(function (t, e) { return e.score - t.score }) }, t.Index.prototype.documentVector = function (e) { for (var n = this.documentStore.get(e), i = n.length, r = new t.Vector, o = 0; i > o; o++) { var s = n.elements[o], a = this.tokenStore.get(s)[e].tf, h = this.idf(s); r.insert(this.corpusTokens.indexOf(s), a * h) } return r }, t.Index.prototype.toJSON = function () { return { version: t.version, fields: this._fields, ref: this._ref, tokenizer: this.tokenizerFn.label, documentStore: this.documentStore.toJSON(), tokenStore: this.tokenStore.toJSON(), corpusTokens: this.corpusTokens.toJSON(), pipeline: this.pipeline.toJSON() } }, t.Index.prototype.use = function (t) { var e = Array.prototype.slice.call(arguments, 1); e.unshift(this), t.apply(this, e) }, t.Store = function () { this.store = {}, this.length = 0 }, t.Store.load = function (e) { var n = new this; return n.length = e.length, n.store = Object.keys(e.store).reduce(function (n, i) { return n[i] = t.SortedSet.load(e.store[i]), n }, {}), n }, t.Store.prototype.set = function (t, e) { this.has(t) || this.length++, this.store[t] = e }, t.Store.prototype.get = function (t) { return this.store[t] }, t.Store.prototype.has = function (t) { return t in this.store }, t.Store.prototype.remove = function (t) { this.has(t) && (delete this.store[t], this.length--) }, t.Store.prototype.toJSON = function () { return { store: this.store, length: this.length } }, t.stemmer = function () { var t = { ational: "ate", tional: "tion", enci: "ence", anci: "ance", izer: "ize", bli: "ble", alli: "al", entli: "ent", eli: "e", ousli: "ous", ization: "ize", ation: "ate", ator: "ate", alism: "al", iveness: "ive", fulness: "ful", ousness: "ous", aliti: "al", iviti: "ive", biliti: "ble", logi: "log" }, e = { icate: "ic", ative: "", alize: "al", iciti: "ic", ical: "ic", ful: "", ness: "" }, n = "[^aeiou]", i = "[aeiouy]", r = n + "[^aeiouy]*", o = i + "[aeiou]*", s = "^(" + r + ")?" + o + r, a = "^(" + r + ")?" + o + r + "(" + o + ")?$", h = "^(" + r + ")?" + o + r + o + r, u = "^(" + r + ")?" + i, l = new RegExp(s), c = new RegExp(h), f = new RegExp(a), d = new RegExp(u), p = /^(.+?)(ss|i)es$/, v = /^(.+?)([^s])s$/, g = /^(.+?)eed$/, m = /^(.+?)(ed|ing)$/, y = /.$/, S = /(at|bl|iz)$/, w = new RegExp("([^aeiouylsz])\\1$"), k = new RegExp("^" + r + i + "[^aeiouwxy]$"), x = /^(.+?[^aeiou])y$/, b = /^(.+?)(ational|tional|enci|anci|izer|bli|alli|entli|eli|ousli|ization|ation|ator|alism|iveness|fulness|ousness|aliti|iviti|biliti|logi)$/, E = /^(.+?)(icate|ative|alize|iciti|ical|ful|ness)$/, F = /^(.+?)(al|ance|ence|er|ic|able|ible|ant|ement|ment|ent|ou|ism|ate|iti|ous|ive|ize)$/, _ = /^(.+?)(s|t)(ion)$/, z = /^(.+?)e$/, O = /ll$/, P = new RegExp("^" + r + i + "[^aeiouwxy]$"), T = function (n) { var i, r, o, s, a, h, u; if (n.length < 3) return n; if (o = n.substr(0, 1), "y" == o && (n = o.toUpperCase() + n.substr(1)), s = p, a = v, s.test(n) ? n = n.replace(s, "$1$2") : a.test(n) && (n = n.replace(a, "$1$2")), s = g, a = m, s.test(n)) { var T = s.exec(n); s = l, s.test(T[1]) && (s = y, n = n.replace(s, "")) } else if (a.test(n)) { var T = a.exec(n); i = T[1], a = d, a.test(i) && (n = i, a = S, h = w, u = k, a.test(n) ? n += "e" : h.test(n) ? (s = y, n = n.replace(s, "")) : u.test(n) && (n += "e")) } if (s = x, s.test(n)) { var T = s.exec(n); i = T[1], n = i + "i" } if (s = b, s.test(n)) { var T = s.exec(n); i = T[1], r = T[2], s = l, s.test(i) && (n = i + t[r]) } if (s = E, s.test(n)) { var T = s.exec(n); i = T[1], r = T[2], s = l, s.test(i) && (n = i + e[r]) } if (s = F, a = _, s.test(n)) { var T = s.exec(n); i = T[1], s = c, s.test(i) && (n = i) } else if (a.test(n)) { var T = a.exec(n); i = T[1] + T[2], a = c, a.test(i) && (n = i) } if (s = z, s.test(n)) { var T = s.exec(n); i = T[1], s = c, a = f, h = P, (s.test(i) || a.test(i) && !h.test(i)) && (n = i) } return s = O, a = c, s.test(n) && a.test(n) && (s = y, n = n.replace(s, "")), "y" == o && (n = o.toLowerCase() + n.substr(1)), n }; return T }(), t.Pipeline.registerFunction(t.stemmer, "stemmer"), t.generateStopWordFilter = function (t) { var e = t.reduce(function (t, e) { return t[e] = e, t }, {}); return function (t) { return t && e[t] !== t ? t : void 0 } }, t.stopWordFilter = t.generateStopWordFilter(["a", "able", "about", "across", "after", "all", "almost", "also", "am", "among", "an", "and", "any", "are", "as", "at", "be", "because", "been", "but", "by", "can", "cannot", "could", "dear", "did", "do", "does", "either", "else", "ever", "every", "for", "from", "get", "got", "had", "has", "have", "he", "her", "hers", "him", "his", "how", "however", "i", "if", "in", "into", "is", "it", "its", "just", "least", "let", "like", "likely", "may", "me", "might", "most", "must", "my", "neither", "no", "nor", "not", "of", "off", "often", "on", "only", "or", "other", "our", "own", "rather", "said", "say", "says", "she", "should", "since", "so", "some", "than", "that", "the", "their", "them", "then", "there", "these", "they", "this", "tis", "to", "too", "twas", "us", "wants", "was", "we", "were", "what", "when", "where", "which", "while", "who", "whom", "why", "will", "with", "would", "yet", "you", "your"]), t.Pipeline.registerFunction(t.stopWordFilter, "stopWordFilter"), t.trimmer = function (t) { return t.replace(/^\W+/, "").replace(/\W+$/, "") }, t.Pipeline.registerFunction(t.trimmer, "trimmer"), t.TokenStore = function () { this.root = { docs: {} }, this.length = 0 }, t.TokenStore.load = function (t) { var e = new this; return e.root = t.root, e.length = t.length, e }, t.TokenStore.prototype.add = function (t, e, n) { var n = n || this.root, i = t.charAt(0), r = t.slice(1); return i in n || (n[i] = { docs: {} }), 0 === r.length ? (n[i].docs[e.ref] = e, void (this.length += 1)) : this.add(r, e, n[i]) }, t.TokenStore.prototype.has = function (t) { if (!t) return !1; for (var e = this.root, n = 0; n < t.length; n++) { if (!e[t.charAt(n)]) return !1; e = e[t.charAt(n)] } return !0 }, t.TokenStore.prototype.getNode = function (t) { if (!t) return {}; for (var e = this.root, n = 0; n < t.length; n++) { if (!e[t.charAt(n)]) return {}; e = e[t.charAt(n)] } return e }, t.TokenStore.prototype.get = function (t, e) { return this.getNode(t, e).docs || {} }, t.TokenStore.prototype.count = function (t, e) { return Object.keys(this.get(t, e)).length }, t.TokenStore.prototype.remove = function (t, e) { if (t) { for (var n = this.root, i = 0; i < t.length; i++) { if (!(t.charAt(i) in n)) return; n = n[t.charAt(i)] } delete n.docs[e] } }, t.TokenStore.prototype.expand = function (t, e) { var n = this.getNode(t), i = n.docs || {}, e = e || []; return Object.keys(i).length && e.push(t), Object.keys(n).forEach(function (n) { "docs" !== n && e.concat(this.expand(t + n, e)) }, this), e }, t.TokenStore.prototype.toJSON = function () { return { root: this.root, length: this.length } }, function (t, e) { "function" == typeof define && define.amd ? define(e) : "object" == typeof exports ? module.exports = e() : t.lunr = e() }(this, function () { return t }) }();
    </script>
    <script>
        /*!
 * Snowball JavaScript Library v0.3
 * http://code.google.com/p/urim/
 * http://snowball.tartarus.org/
 *
 * Copyright 2010, Oleg Mazko
 * http://www.mozilla.org/MPL/
 */

        /**
         * export the module via AMD, CommonJS or as a browser global
         * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js
         */
        ; (function (root, factory) {
            if (typeof define === 'function' && define.amd) {
                // AMD. Register as an anonymous module.
                define(factory)
            } else if (typeof exports === 'object') {
                /**
                 * Node. Does not work with strict CommonJS, but
                 * only CommonJS-like environments that support module.exports,
                 * like Node.
                 */
                module.exports = factory()
            } else {
                // Browser globals (root is window)
                factory()(root.lunr);
            }
        }(this, function () {
            /**
             * Just return a value to define the module export.
             * This example returns an object, but the module
             * can return a function as the exported value.
             */
            return function (lunr) {
                /* provides utilities for the included stemmers */
                lunr.stemmerSupport = {
                    Among: function (s, substring_i, result, method) {
                        this.toCharArray = function (s) {
                            var sLength = s.length, charArr = new Array(sLength);
                            for (var i = 0; i < sLength; i++)
                                charArr[i] = s.charCodeAt(i);
                            return charArr;
                        };

                        if ((!s && s != "") || (!substring_i && (substring_i != 0)) || !result)
                            throw ("Bad Among initialisation: s:" + s + ", substring_i: "
                                + substring_i + ", result: " + result);
                        this.s_size = s.length;
                        this.s = this.toCharArray(s);
                        this.substring_i = substring_i;
                        this.result = result;
                        this.method = method;
                    },
                    SnowballProgram: function () {
                        var current;
                        return {
                            bra: 0,
                            ket: 0,
                            limit: 0,
                            cursor: 0,
                            limit_backward: 0,
                            setCurrent: function (word) {
                                current = word;
                                this.cursor = 0;
                                this.limit = word.length;
                                this.limit_backward = 0;
                                this.bra = this.cursor;
                                this.ket = this.limit;
                            },
                            getCurrent: function () {
                                var result = current;
                                current = null;
                                return result;
                            },
                            in_grouping: function (s, min, max) {
                                if (this.cursor < this.limit) {
                                    var ch = current.charCodeAt(this.cursor);
                                    if (ch <= max && ch >= min) {
                                        ch -= min;
                                        if (s[ch >> 3] & (0X1 << (ch & 0X7))) {
                                            this.cursor++;
                                            return true;
                                        }
                                    }
                                }
                                return false;
                            },
                            in_grouping_b: function (s, min, max) {
                                if (this.cursor > this.limit_backward) {
                                    var ch = current.charCodeAt(this.cursor - 1);
                                    if (ch <= max && ch >= min) {
                                        ch -= min;
                                        if (s[ch >> 3] & (0X1 << (ch & 0X7))) {
                                            this.cursor--;
                                            return true;
                                        }
                                    }
                                }
                                return false;
                            },
                            out_grouping: function (s, min, max) {
                                if (this.cursor < this.limit) {
                                    var ch = current.charCodeAt(this.cursor);
                                    if (ch > max || ch < min) {
                                        this.cursor++;
                                        return true;
                                    }
                                    ch -= min;
                                    if (!(s[ch >> 3] & (0X1 << (ch & 0X7)))) {
                                        this.cursor++;
                                        return true;
                                    }
                                }
                                return false;
                            },
                            out_grouping_b: function (s, min, max) {
                                if (this.cursor > this.limit_backward) {
                                    var ch = current.charCodeAt(this.cursor - 1);
                                    if (ch > max || ch < min) {
                                        this.cursor--;
                                        return true;
                                    }
                                    ch -= min;
                                    if (!(s[ch >> 3] & (0X1 << (ch & 0X7)))) {
                                        this.cursor--;
                                        return true;
                                    }
                                }
                                return false;
                            },
                            eq_s: function (s_size, s) {
                                if (this.limit - this.cursor < s_size)
                                    return false;
                                for (var i = 0; i < s_size; i++)
                                    if (current.charCodeAt(this.cursor + i) != s.charCodeAt(i))
                                        return false;
                                this.cursor += s_size;
                                return true;
                            },
                            eq_s_b: function (s_size, s) {
                                if (this.cursor - this.limit_backward < s_size)
                                    return false;
                                for (var i = 0; i < s_size; i++)
                                    if (current.charCodeAt(this.cursor - s_size + i) != s
                                        .charCodeAt(i))
                                        return false;
                                this.cursor -= s_size;
                                return true;
                            },
                            find_among: function (v, v_size) {
                                var i = 0, j = v_size, c = this.cursor, l = this.limit, common_i = 0, common_j = 0, first_key_inspected = false;
                                while (true) {
                                    var k = i + ((j - i) >> 1), diff = 0, common = common_i < common_j
                                        ? common_i
                                        : common_j, w = v[k];
                                    for (var i2 = common; i2 < w.s_size; i2++) {
                                        if (c + common == l) {
                                            diff = -1;
                                            break;
                                        }
                                        diff = current.charCodeAt(c + common) - w.s[i2];
                                        if (diff)
                                            break;
                                        common++;
                                    }
                                    if (diff < 0) {
                                        j = k;
                                        common_j = common;
                                    } else {
                                        i = k;
                                        common_i = common;
                                    }
                                    if (j - i <= 1) {
                                        if (i > 0 || j == i || first_key_inspected)
                                            break;
                                        first_key_inspected = true;
                                    }
                                }
                                while (true) {
                                    var w = v[i];
                                    if (common_i >= w.s_size) {
                                        this.cursor = c + w.s_size;
                                        if (!w.method)
                                            return w.result;
                                        var res = w.method();
                                        this.cursor = c + w.s_size;
                                        if (res)
                                            return w.result;
                                    }
                                    i = w.substring_i;
                                    if (i < 0)
                                        return 0;
                                }
                            },
                            find_among_b: function (v, v_size) {
                                var i = 0, j = v_size, c = this.cursor, lb = this.limit_backward, common_i = 0, common_j = 0, first_key_inspected = false;
                                while (true) {
                                    var k = i + ((j - i) >> 1), diff = 0, common = common_i < common_j
                                        ? common_i
                                        : common_j, w = v[k];
                                    for (var i2 = w.s_size - 1 - common; i2 >= 0; i2--) {
                                        if (c - common == lb) {
                                            diff = -1;
                                            break;
                                        }
                                        diff = current.charCodeAt(c - 1 - common) - w.s[i2];
                                        if (diff)
                                            break;
                                        common++;
                                    }
                                    if (diff < 0) {
                                        j = k;
                                        common_j = common;
                                    } else {
                                        i = k;
                                        common_i = common;
                                    }
                                    if (j - i <= 1) {
                                        if (i > 0 || j == i || first_key_inspected)
                                            break;
                                        first_key_inspected = true;
                                    }
                                }
                                while (true) {
                                    var w = v[i];
                                    if (common_i >= w.s_size) {
                                        this.cursor = c - w.s_size;
                                        if (!w.method)
                                            return w.result;
                                        var res = w.method();
                                        this.cursor = c - w.s_size;
                                        if (res)
                                            return w.result;
                                    }
                                    i = w.substring_i;
                                    if (i < 0)
                                        return 0;
                                }
                            },
                            replace_s: function (c_bra, c_ket, s) {
                                var adjustment = s.length - (c_ket - c_bra), left = current
                                    .substring(0, c_bra), right = current.substring(c_ket);
                                current = left + s + right;
                                this.limit += adjustment;
                                if (this.cursor >= c_ket)
                                    this.cursor += adjustment;
                                else if (this.cursor > c_bra)
                                    this.cursor = c_bra;
                                return adjustment;
                            },
                            slice_check: function () {
                                if (this.bra < 0 || this.bra > this.ket || this.ket > this.limit
                                    || this.limit > current.length)
                                    throw ("faulty slice operation");
                            },
                            slice_from: function (s) {
                                this.slice_check();
                                this.replace_s(this.bra, this.ket, s);
                            },
                            slice_del: function () {
                                this.slice_from("");
                            },
                            insert: function (c_bra, c_ket, s) {
                                var adjustment = this.replace_s(c_bra, c_ket, s);
                                if (c_bra <= this.bra)
                                    this.bra += adjustment;
                                if (c_bra <= this.ket)
                                    this.ket += adjustment;
                            },
                            slice_to: function () {
                                this.slice_check();
                                return current.substring(this.bra, this.ket);
                            },
                            eq_v_b: function (s) {
                                return this.eq_s_b(s.length, s);
                            }
                        };
                    }
                };

                lunr.trimmerSupport = {
                    generateTrimmer: function (wordCharacters) {
                        var startRegex = new RegExp("^[^" + wordCharacters + "]+")
                        var endRegex = new RegExp("[^" + wordCharacters + "]+$")

                        return function (token) {
                            // for lunr version 2
                            if (typeof token.update === "function") {
                                return token.update(function (s) {
                                    return s
                                        .replace(startRegex, '')
                                        .replace(endRegex, '');
                                })
                            } else { // for lunr version 1
                                return token
                                    .replace(startRegex, '')
                                    .replace(endRegex, '');
                            }
                        };
                    }
                }
            }
        }));

</script>
    <script>
        /*!
        * Lunr languages, `Chinese` language
        * https://github.com/MihaiValentin/lunr-languages
        *
        * Copyright 2019, Felix Lian (repairearth)
        * http://www.mozilla.org/MPL/
        */
        /*!
         * based on
         * Snowball zhvaScript Library v0.3
         * http://code.google.com/p/urim/
         * http://snowball.tartarus.org/
         *
         * Copyright 2010, Oleg Mazko
         * http://www.mozilla.org/MPL/
         */

        /**
         * export the module via AMD, CommonJS or as a browser global
         * Export code from https://github.com/umdjs/umd/blob/master/returnExports.js
         */
        ;
        (function (root, factory) {
            if (typeof define === 'function' && define.amd) {
                // AMD. Register as an anonymous module.
                define(factory)
            } else if (typeof exports === 'object') {
                /**
                 * Node. Does not work with strict CommonJS, but
                 * only CommonJS-like environments that support module.exports,
                 * like Node.
                 */
                // module.exports = factory(require('@node-rs/jieba'))
                module.exports = factory()
            } else {
                // Browser globals (root is window)
                factory()(root.lunr);
            }
        }(this, function (Segment) {
            /**
             * Just return a value to define the module export.
             * This example returns an object, but the module
             * can return a function as the exported value.
             */
            return function (lunr) {
                /* throw error if lunr is not yet included */
                if ('undefined' === typeof lunr) {
                    throw new Error('Lunr is not present. Please include / require Lunr before this script.');
                }

                /* throw error if lunr stemmer support is not yet included */
                if ('undefined' === typeof lunr.stemmerSupport) {
                    throw new Error('Lunr stemmer support is not present. Please include / require Lunr stemmer support before this script.');
                }

                /*
                Chinese tokenization is trickier, since it does not
                take into account spaces.
                Since the tokenization function is represented different
                internally for each of the Lunr versions, this had to be done
                in order to try to try to pick the best way of doing this based
                on the Lunr version
                 */
                var isLunr2 = lunr.version[0] == "2";

                /* register specific locale function */
                lunr.zh = function () {
                    this.pipeline.reset();
                    this.pipeline.add(
                        lunr.zh.trimmer,
                        lunr.zh.stopWordFilter,
                        lunr.zh.stemmer
                    );

                    // change the tokenizer for Chinese one
                    if (isLunr2) { // for lunr version 2.0.0
                        this.tokenizer = lunr.zh.tokenizer;
                    } else {
                        if (lunr.tokenizer) { // for lunr version 0.6.0
                            lunr.tokenizer = lunr.zh.tokenizer;
                        }
                        if (this.tokenizerFn) { // for lunr version 0.7.0 -> 1.0.0
                            this.tokenizerFn = lunr.zh.tokenizer;
                        }
                    }
                };

                lunr.zh.tokenizer = function (obj) {
                    if (!arguments.length || obj == null || obj == undefined) return []
                    if (Array.isArray(obj)) return obj.map(function (t) {
                        return isLunr2 ? new lunr.Token(t.toLowerCase()) : t.toLowerCase()
                    })

                    var tokens = [];
                    try {
                        var segmenter = new Intl.Segmenter('zh', { granularity: 'word' });
                        var str = obj.toString().trim().toLowerCase();
                        var segments = segmenter.segment(str)
                        for (seg of segments) {
                            if (seg.isWordLike) { tokens = tokens.concat(seg.segment) }
                        }
                    } catch (error) {
                        console.error(error);
                    }

                    tokens = tokens.filter(function (token) {
                        return !!token;
                    });

                    var fromIndex = 0

                    return tokens.map(function (token, index) {
                        if (isLunr2) {
                            var start = str.indexOf(token, fromIndex)

                            var tokenMetadata = {}
                            tokenMetadata["position"] = [start, token.length]
                            tokenMetadata["index"] = index

                            fromIndex = start

                            return new lunr.Token(token, tokenMetadata);
                        } else {
                            return token
                        }
                    });
                }

                /* lunr trimmer function */
                lunr.zh.wordCharacters = "\\w\u4e00-\u9fa5";
                lunr.zh.trimmer = lunr.trimmerSupport.generateTrimmer(lunr.zh.wordCharacters);
                lunr.Pipeline.registerFunction(lunr.zh.trimmer, 'trimmer-zh');

                /* lunr stemmer function */
                lunr.zh.stemmer = (function () {

                    /* TODO Chinese stemmer  */
                    return function (word) {
                        return word;
                    }
                })();
                lunr.Pipeline.registerFunction(lunr.zh.stemmer, 'stemmer-zh');

                /* lunr stop word filter. see https://www.ranks.nl/stopwords/chinese-stopwords */
                lunr.zh.stopWordFilter = lunr.generateStopWordFilter(
                    '的 一 不 在 人 有 是 为 以 于 上 他 而 后 之 来 及 了 因 下 可 到 由 这 与 也 此 但 并 个 其 已 无 小 我 们 起 最 再 今 去 好 只 又 或 很 亦 某 把 那 你 乃 它 吧 被 比 别 趁 当 从 到 得 打 凡 儿 尔 该 各 给 跟 和 何 还 即 几 既 看 据 距 靠 啦 了 另 么 每 们 嘛 拿 哪 那 您 凭 且 却 让 仍 啥 如 若 使 谁 虽 随 同 所 她 哇 嗡 往 哪 些 向 沿 哟 用 于 咱 则 怎 曾 至 致 着 诸 自'.split(' '));
                lunr.Pipeline.registerFunction(lunr.zh.stopWordFilter, 'stopWordFilter-zh');
            };
        }))
    </script>
    
    <script>
        !function (n) { "use strict"; function t(n, t) { var r = (65535 & n) + (65535 & t); return (n >> 16) + (t >> 16) + (r >> 16) << 16 | 65535 & r } function r(n, t) { return n << t | n >>> 32 - t } function e(n, e, o, u, c, f) { return t(r(t(t(e, n), t(u, f)), c), o) } function o(n, t, r, o, u, c, f) { return e(t & r | ~t & o, n, t, u, c, f) } function u(n, t, r, o, u, c, f) { return e(t & o | r & ~o, n, t, u, c, f) } function c(n, t, r, o, u, c, f) { return e(t ^ r ^ o, n, t, u, c, f) } function f(n, t, r, o, u, c, f) { return e(r ^ (t | ~o), n, t, u, c, f) } function i(n, r) { n[r >> 5] |= 128 << r % 32, n[14 + (r + 64 >>> 9 << 4)] = r; var e, i, a, d, h, l = 1732584193, g = -271733879, v = -1732584194, m = 271733878; for (e = 0; e < n.length; e += 16)i = l, a = g, d = v, h = m, g = f(g = f(g = f(g = f(g = c(g = c(g = c(g = c(g = u(g = u(g = u(g = u(g = o(g = o(g = o(g = o(g, v = o(v, m = o(m, l = o(l, g, v, m, n[e], 7, -680876936), g, v, n[e + 1], 12, -389564586), l, g, n[e + 2], 17, 606105819), m, l, n[e + 3], 22, -1044525330), v = o(v, m = o(m, l = o(l, g, v, m, n[e + 4], 7, -176418897), g, v, n[e + 5], 12, 1200080426), l, g, n[e + 6], 17, -1473231341), m, l, n[e + 7], 22, -45705983), v = o(v, m = o(m, l = o(l, g, v, m, n[e + 8], 7, 1770035416), g, v, n[e + 9], 12, -1958414417), l, g, n[e + 10], 17, -42063), m, l, n[e + 11], 22, -1990404162), v = o(v, m = o(m, l = o(l, g, v, m, n[e + 12], 7, 1804603682), g, v, n[e + 13], 12, -40341101), l, g, n[e + 14], 17, -1502002290), m, l, n[e + 15], 22, 1236535329), v = u(v, m = u(m, l = u(l, g, v, m, n[e + 1], 5, -165796510), g, v, n[e + 6], 9, -1069501632), l, g, n[e + 11], 14, 643717713), m, l, n[e], 20, -373897302), v = u(v, m = u(m, l = u(l, g, v, m, n[e + 5], 5, -701558691), g, v, n[e + 10], 9, 38016083), l, g, n[e + 15], 14, -660478335), m, l, n[e + 4], 20, -405537848), v = u(v, m = u(m, l = u(l, g, v, m, n[e + 9], 5, 568446438), g, v, n[e + 14], 9, -1019803690), l, g, n[e + 3], 14, -187363961), m, l, n[e + 8], 20, 1163531501), v = u(v, m = u(m, l = u(l, g, v, m, n[e + 13], 5, -1444681467), g, v, n[e + 2], 9, -51403784), l, g, n[e + 7], 14, 1735328473), m, l, n[e + 12], 20, -1926607734), v = c(v, m = c(m, l = c(l, g, v, m, n[e + 5], 4, -378558), g, v, n[e + 8], 11, -2022574463), l, g, n[e + 11], 16, 1839030562), m, l, n[e + 14], 23, -35309556), v = c(v, m = c(m, l = c(l, g, v, m, n[e + 1], 4, -1530992060), g, v, n[e + 4], 11, 1272893353), l, g, n[e + 7], 16, -155497632), m, l, n[e + 10], 23, -1094730640), v = c(v, m = c(m, l = c(l, g, v, m, n[e + 13], 4, 681279174), g, v, n[e], 11, -358537222), l, g, n[e + 3], 16, -722521979), m, l, n[e + 6], 23, 76029189), v = c(v, m = c(m, l = c(l, g, v, m, n[e + 9], 4, -640364487), g, v, n[e + 12], 11, -421815835), l, g, n[e + 15], 16, 530742520), m, l, n[e + 2], 23, -995338651), v = f(v, m = f(m, l = f(l, g, v, m, n[e], 6, -198630844), g, v, n[e + 7], 10, 1126891415), l, g, n[e + 14], 15, -1416354905), m, l, n[e + 5], 21, -57434055), v = f(v, m = f(m, l = f(l, g, v, m, n[e + 12], 6, 1700485571), g, v, n[e + 3], 10, -1894986606), l, g, n[e + 10], 15, -1051523), m, l, n[e + 1], 21, -2054922799), v = f(v, m = f(m, l = f(l, g, v, m, n[e + 8], 6, 1873313359), g, v, n[e + 15], 10, -30611744), l, g, n[e + 6], 15, -1560198380), m, l, n[e + 13], 21, 1309151649), v = f(v, m = f(m, l = f(l, g, v, m, n[e + 4], 6, -145523070), g, v, n[e + 11], 10, -1120210379), l, g, n[e + 2], 15, 718787259), m, l, n[e + 9], 21, -343485551), l = t(l, i), g = t(g, a), v = t(v, d), m = t(m, h); return [l, g, v, m] } function a(n) { var t, r = "", e = 32 * n.length; for (t = 0; t < e; t += 8)r += String.fromCharCode(n[t >> 5] >>> t % 32 & 255); return r } function d(n) { var t, r = []; for (r[(n.length >> 2) - 1] = void 0, t = 0; t < r.length; t += 1)r[t] = 0; var e = 8 * n.length; for (t = 0; t < e; t += 8)r[t >> 5] |= (255 & n.charCodeAt(t / 8)) << t % 32; return r } function h(n) { return a(i(d(n), 8 * n.length)) } function l(n, t) { var r, e, o = d(n), u = [], c = []; for (u[15] = c[15] = void 0, o.length > 16 && (o = i(o, 8 * n.length)), r = 0; r < 16; r += 1)u[r] = 909522486 ^ o[r], c[r] = 1549556828 ^ o[r]; return e = i(u.concat(d(t)), 512 + 8 * t.length), a(i(c.concat(e), 640)) } function g(n) { var t, r, e = ""; for (r = 0; r < n.length; r += 1)t = n.charCodeAt(r), e += "0123456789abcdef".charAt(t >>> 4 & 15) + "0123456789abcdef".charAt(15 & t); return e } function v(n) { return unescape(encodeURIComponent(n)) } function m(n) { return h(v(n)) } function p(n) { return g(m(n)) } function s(n, t) { return l(v(n), v(t)) } function C(n, t) { return g(s(n, t)) } function A(n, t, r) { return t ? r ? s(t, n) : C(t, n) : r ? m(n) : p(n) } "function" == typeof define && define.amd ? define(function () { return A }) : "object" == typeof module && module.exports ? module.exports = A : n.md5 = A }(this);
    </script>
    <script>
        /* flatpickr v4.6.13,, @license MIT */
        !function (e, n) { "object" == typeof exports && "undefined" != typeof module ? module.exports = n() : "function" == typeof define && define.amd ? define(n) : (e = "undefined" != typeof globalThis ? globalThis : e || self).flatpickr = n() }(this, (function () { "use strict"; var e = function () { return (e = Object.assign || function (e) { for (var n, t = 1, a = arguments.length; t < a; t++)for (var i in n = arguments[t]) Object.prototype.hasOwnProperty.call(n, i) && (e[i] = n[i]); return e }).apply(this, arguments) }; function n() { for (var e = 0, n = 0, t = arguments.length; n < t; n++)e += arguments[n].length; var a = Array(e), i = 0; for (n = 0; n < t; n++)for (var o = arguments[n], r = 0, l = o.length; r < l; r++, i++)a[i] = o[r]; return a } var t = ["onChange", "onClose", "onDayCreate", "onDestroy", "onKeyDown", "onMonthChange", "onOpen", "onParseConfig", "onReady", "onValueUpdate", "onYearChange", "onPreCalendarPosition"], a = { _disable: [], allowInput: !1, allowInvalidPreload: !1, altFormat: "F j, Y", altInput: !1, altInputClass: "form-control input", animate: "object" == typeof window && -1 === window.navigator.userAgent.indexOf("MSIE"), ariaDateFormat: "F j, Y", autoFillDefaultTime: !0, clickOpens: !0, closeOnSelect: !0, conjunction: ", ", dateFormat: "Y-m-d", defaultHour: 12, defaultMinute: 0, defaultSeconds: 0, disable: [], disableMobile: !1, enableSeconds: !1, enableTime: !1, errorHandler: function (e) { return "undefined" != typeof console && console.warn(e) }, getWeek: function (e) { var n = new Date(e.getTime()); n.setHours(0, 0, 0, 0), n.setDate(n.getDate() + 3 - (n.getDay() + 6) % 7); var t = new Date(n.getFullYear(), 0, 4); return 1 + Math.round(((n.getTime() - t.getTime()) / 864e5 - 3 + (t.getDay() + 6) % 7) / 7) }, hourIncrement: 1, ignoredFocusElements: [], inline: !1, locale: "default", minuteIncrement: 5, mode: "single", monthSelectorType: "dropdown", nextArrow: "<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M13.207 8.472l-7.854 7.854-0.707-0.707 7.146-7.146-7.146-7.148 0.707-0.707 7.854 7.854z' /></svg>", noCalendar: !1, now: new Date, onChange: [], onClose: [], onDayCreate: [], onDestroy: [], onKeyDown: [], onMonthChange: [], onOpen: [], onParseConfig: [], onReady: [], onValueUpdate: [], onYearChange: [], onPreCalendarPosition: [], plugins: [], position: "auto", positionElement: void 0, prevArrow: "<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M5.207 8.471l7.146 7.147-0.707 0.707-7.853-7.854 7.854-7.853 0.707 0.707-7.147 7.146z' /></svg>", shorthandCurrentMonth: !1, showMonths: 1, static: !1, time_24hr: !1, weekNumbers: !1, wrap: !1 }, i = { weekdays: { shorthand: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"], longhand: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"] }, months: { shorthand: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], longhand: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"] }, daysInMonth: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31], firstDayOfWeek: 0, ordinal: function (e) { var n = e % 100; if (n > 3 && n < 21) return "th"; switch (n % 10) { case 1: return "st"; case 2: return "nd"; case 3: return "rd"; default: return "th" } }, rangeSeparator: " to ", weekAbbreviation: "Wk", scrollTitle: "Scroll to increment", toggleTitle: "Click to toggle", amPM: ["AM", "PM"], yearAriaLabel: "Year", monthAriaLabel: "Month", hourAriaLabel: "Hour", minuteAriaLabel: "Minute", time_24hr: !1 }, o = function (e, n) { return void 0 === n && (n = 2), ("000" + e).slice(-1 * n) }, r = function (e) { return !0 === e ? 1 : 0 }; function l(e, n) { var t; return function () { var a = this, i = arguments; clearTimeout(t), t = setTimeout((function () { return e.apply(a, i) }), n) } } var c = function (e) { return e instanceof Array ? e : [e] }; function s(e, n, t) { if (!0 === t) return e.classList.add(n); e.classList.remove(n) } function d(e, n, t) { var a = window.document.createElement(e); return n = n || "", t = t || "", a.className = n, void 0 !== t && (a.textContent = t), a } function u(e) { for (; e.firstChild;)e.removeChild(e.firstChild) } function f(e, n) { return n(e) ? e : e.parentNode ? f(e.parentNode, n) : void 0 } function m(e, n) { var t = d("div", "numInputWrapper"), a = d("input", "numInput " + e), i = d("span", "arrowUp"), o = d("span", "arrowDown"); if (-1 === navigator.userAgent.indexOf("MSIE 9.0") ? a.type = "number" : (a.type = "text", a.pattern = "\\d*"), void 0 !== n) for (var r in n) a.setAttribute(r, n[r]); return t.appendChild(a), t.appendChild(i), t.appendChild(o), t } function g(e) { try { return "function" == typeof e.composedPath ? e.composedPath()[0] : e.target } catch (n) { return e.target } } var p = function () { }, h = function (e, n, t) { return t.months[n ? "shorthand" : "longhand"][e] }, v = { D: p, F: function (e, n, t) { e.setMonth(t.months.longhand.indexOf(n)) }, G: function (e, n) { e.setHours((e.getHours() >= 12 ? 12 : 0) + parseFloat(n)) }, H: function (e, n) { e.setHours(parseFloat(n)) }, J: function (e, n) { e.setDate(parseFloat(n)) }, K: function (e, n, t) { e.setHours(e.getHours() % 12 + 12 * r(new RegExp(t.amPM[1], "i").test(n))) }, M: function (e, n, t) { e.setMonth(t.months.shorthand.indexOf(n)) }, S: function (e, n) { e.setSeconds(parseFloat(n)) }, U: function (e, n) { return new Date(1e3 * parseFloat(n)) }, W: function (e, n, t) { var a = parseInt(n), i = new Date(e.getFullYear(), 0, 2 + 7 * (a - 1), 0, 0, 0, 0); return i.setDate(i.getDate() - i.getDay() + t.firstDayOfWeek), i }, Y: function (e, n) { e.setFullYear(parseFloat(n)) }, Z: function (e, n) { return new Date(n) }, d: function (e, n) { e.setDate(parseFloat(n)) }, h: function (e, n) { e.setHours((e.getHours() >= 12 ? 12 : 0) + parseFloat(n)) }, i: function (e, n) { e.setMinutes(parseFloat(n)) }, j: function (e, n) { e.setDate(parseFloat(n)) }, l: p, m: function (e, n) { e.setMonth(parseFloat(n) - 1) }, n: function (e, n) { e.setMonth(parseFloat(n) - 1) }, s: function (e, n) { e.setSeconds(parseFloat(n)) }, u: function (e, n) { return new Date(parseFloat(n)) }, w: p, y: function (e, n) { e.setFullYear(2e3 + parseFloat(n)) } }, D = { D: "", F: "", G: "(\\d\\d|\\d)", H: "(\\d\\d|\\d)", J: "(\\d\\d|\\d)\\w+", K: "", M: "", S: "(\\d\\d|\\d)", U: "(.+)", W: "(\\d\\d|\\d)", Y: "(\\d{4})", Z: "(.+)", d: "(\\d\\d|\\d)", h: "(\\d\\d|\\d)", i: "(\\d\\d|\\d)", j: "(\\d\\d|\\d)", l: "", m: "(\\d\\d|\\d)", n: "(\\d\\d|\\d)", s: "(\\d\\d|\\d)", u: "(.+)", w: "(\\d\\d|\\d)", y: "(\\d{2})" }, w = { Z: function (e) { return e.toISOString() }, D: function (e, n, t) { return n.weekdays.shorthand[w.w(e, n, t)] }, F: function (e, n, t) { return h(w.n(e, n, t) - 1, !1, n) }, G: function (e, n, t) { return o(w.h(e, n, t)) }, H: function (e) { return o(e.getHours()) }, J: function (e, n) { return void 0 !== n.ordinal ? e.getDate() + n.ordinal(e.getDate()) : e.getDate() }, K: function (e, n) { return n.amPM[r(e.getHours() > 11)] }, M: function (e, n) { return h(e.getMonth(), !0, n) }, S: function (e) { return o(e.getSeconds()) }, U: function (e) { return e.getTime() / 1e3 }, W: function (e, n, t) { return t.getWeek(e) }, Y: function (e) { return o(e.getFullYear(), 4) }, d: function (e) { return o(e.getDate()) }, h: function (e) { return e.getHours() % 12 ? e.getHours() % 12 : 12 }, i: function (e) { return o(e.getMinutes()) }, j: function (e) { return e.getDate() }, l: function (e, n) { return n.weekdays.longhand[e.getDay()] }, m: function (e) { return o(e.getMonth() + 1) }, n: function (e) { return e.getMonth() + 1 }, s: function (e) { return e.getSeconds() }, u: function (e) { return e.getTime() }, w: function (e) { return e.getDay() }, y: function (e) { return String(e.getFullYear()).substring(2) } }, b = function (e) { var n = e.config, t = void 0 === n ? a : n, o = e.l10n, r = void 0 === o ? i : o, l = e.isMobile, c = void 0 !== l && l; return function (e, n, a) { var i = a || r; return void 0 === t.formatDate || c ? n.split("").map((function (n, a, o) { return w[n] && "\\" !== o[a - 1] ? w[n](e, i, t) : "\\" !== n ? n : "" })).join("") : t.formatDate(e, n, i) } }, C = function (e) { var n = e.config, t = void 0 === n ? a : n, o = e.l10n, r = void 0 === o ? i : o; return function (e, n, i, o) { if (0 === e || e) { var l, c = o || r, s = e; if (e instanceof Date) l = new Date(e.getTime()); else if ("string" != typeof e && void 0 !== e.toFixed) l = new Date(e); else if ("string" == typeof e) { var d = n || (t || a).dateFormat, u = String(e).trim(); if ("today" === u) l = new Date, i = !0; else if (t && t.parseDate) l = t.parseDate(e, d); else if (/Z$/.test(u) || /GMT$/.test(u)) l = new Date(e); else { for (var f = void 0, m = [], g = 0, p = 0, h = ""; g < d.length; g++) { var w = d[g], b = "\\" === w, C = "\\" === d[g - 1] || b; if (D[w] && !C) { h += D[w]; var M = new RegExp(h).exec(e); M && (f = !0) && m["Y" !== w ? "push" : "unshift"]({ fn: v[w], val: M[++p] }) } else b || (h += ".") } l = t && t.noCalendar ? new Date((new Date).setHours(0, 0, 0, 0)) : new Date((new Date).getFullYear(), 0, 1, 0, 0, 0, 0), m.forEach((function (e) { var n = e.fn, t = e.val; return l = n(l, t, c) || l })), l = f ? l : void 0 } } if (l instanceof Date && !isNaN(l.getTime())) return !0 === i && l.setHours(0, 0, 0, 0), l; t.errorHandler(new Error("Invalid date provided: " + s)) } } }; function M(e, n, t) { return void 0 === t && (t = !0), !1 !== t ? new Date(e.getTime()).setHours(0, 0, 0, 0) - new Date(n.getTime()).setHours(0, 0, 0, 0) : e.getTime() - n.getTime() } var y = function (e, n, t) { return 3600 * e + 60 * n + t }, x = 864e5; function E(e) { var n = e.defaultHour, t = e.defaultMinute, a = e.defaultSeconds; if (void 0 !== e.minDate) { var i = e.minDate.getHours(), o = e.minDate.getMinutes(), r = e.minDate.getSeconds(); n < i && (n = i), n === i && t < o && (t = o), n === i && t === o && a < r && (a = e.minDate.getSeconds()) } if (void 0 !== e.maxDate) { var l = e.maxDate.getHours(), c = e.maxDate.getMinutes(); (n = Math.min(n, l)) === l && (t = Math.min(c, t)), n === l && t === c && (a = e.maxDate.getSeconds()) } return { hours: n, minutes: t, seconds: a } } "function" != typeof Object.assign && (Object.assign = function (e) { for (var n = [], t = 1; t < arguments.length; t++)n[t - 1] = arguments[t]; if (!e) throw TypeError("Cannot convert undefined or null to object"); for (var a = function (n) { n && Object.keys(n).forEach((function (t) { return e[t] = n[t] })) }, i = 0, o = n; i < o.length; i++) { var r = o[i]; a(r) } return e }); function k(p, v) { var w = { config: e(e({}, a), I.defaultConfig), l10n: i }; function k() { var e; return (null === (e = w.calendarContainer) || void 0 === e ? void 0 : e.getRootNode()).activeElement || document.activeElement } function T(e) { return e.bind(w) } function S() { var e = w.config; !1 === e.weekNumbers && 1 === e.showMonths || !0 !== e.noCalendar && window.requestAnimationFrame((function () { if (void 0 !== w.calendarContainer && (w.calendarContainer.style.visibility = "hidden", w.calendarContainer.style.display = "block"), void 0 !== w.daysContainer) { var n = (w.days.offsetWidth + 1) * e.showMonths; w.daysContainer.style.width = n + "px", w.calendarContainer.style.width = n + (void 0 !== w.weekWrapper ? w.weekWrapper.offsetWidth : 0) + "px", w.calendarContainer.style.removeProperty("visibility"), w.calendarContainer.style.removeProperty("display") } })) } function _(e) { if (0 === w.selectedDates.length) { var n = void 0 === w.config.minDate || M(new Date, w.config.minDate) >= 0 ? new Date : new Date(w.config.minDate.getTime()), t = E(w.config); n.setHours(t.hours, t.minutes, t.seconds, n.getMilliseconds()), w.selectedDates = [n], w.latestSelectedDateObj = n } void 0 !== e && "blur" !== e.type && function (e) { e.preventDefault(); var n = "keydown" === e.type, t = g(e), a = t; void 0 !== w.amPM && t === w.amPM && (w.amPM.textContent = w.l10n.amPM[r(w.amPM.textContent === w.l10n.amPM[0])]); var i = parseFloat(a.getAttribute("min")), l = parseFloat(a.getAttribute("max")), c = parseFloat(a.getAttribute("step")), s = parseInt(a.value, 10), d = e.delta || (n ? 38 === e.which ? 1 : -1 : 0), u = s + c * d; if (void 0 !== a.value && 2 === a.value.length) { var f = a === w.hourElement, m = a === w.minuteElement; u < i ? (u = l + u + r(!f) + (r(f) && r(!w.amPM)), m && L(void 0, -1, w.hourElement)) : u > l && (u = a === w.hourElement ? u - l - r(!w.amPM) : i, m && L(void 0, 1, w.hourElement)), w.amPM && f && (1 === c ? u + s === 23 : Math.abs(u - s) > c) && (w.amPM.textContent = w.l10n.amPM[r(w.amPM.textContent === w.l10n.amPM[0])]), a.value = o(u) } }(e); var a = w._input.value; O(), ye(), w._input.value !== a && w._debouncedChange() } function O() { if (void 0 !== w.hourElement && void 0 !== w.minuteElement) { var e, n, t = (parseInt(w.hourElement.value.slice(-2), 10) || 0) % 24, a = (parseInt(w.minuteElement.value, 10) || 0) % 60, i = void 0 !== w.secondElement ? (parseInt(w.secondElement.value, 10) || 0) % 60 : 0; void 0 !== w.amPM && (e = t, n = w.amPM.textContent, t = e % 12 + 12 * r(n === w.l10n.amPM[1])); var o = void 0 !== w.config.minTime || w.config.minDate && w.minDateHasTime && w.latestSelectedDateObj && 0 === M(w.latestSelectedDateObj, w.config.minDate, !0), l = void 0 !== w.config.maxTime || w.config.maxDate && w.maxDateHasTime && w.latestSelectedDateObj && 0 === M(w.latestSelectedDateObj, w.config.maxDate, !0); if (void 0 !== w.config.maxTime && void 0 !== w.config.minTime && w.config.minTime > w.config.maxTime) { var c = y(w.config.minTime.getHours(), w.config.minTime.getMinutes(), w.config.minTime.getSeconds()), s = y(w.config.maxTime.getHours(), w.config.maxTime.getMinutes(), w.config.maxTime.getSeconds()), d = y(t, a, i); if (d > s && d < c) { var u = function (e) { var n = Math.floor(e / 3600), t = (e - 3600 * n) / 60; return [n, t, e - 3600 * n - 60 * t] }(c); t = u[0], a = u[1], i = u[2] } } else { if (l) { var f = void 0 !== w.config.maxTime ? w.config.maxTime : w.config.maxDate; (t = Math.min(t, f.getHours())) === f.getHours() && (a = Math.min(a, f.getMinutes())), a === f.getMinutes() && (i = Math.min(i, f.getSeconds())) } if (o) { var m = void 0 !== w.config.minTime ? w.config.minTime : w.config.minDate; (t = Math.max(t, m.getHours())) === m.getHours() && a < m.getMinutes() && (a = m.getMinutes()), a === m.getMinutes() && (i = Math.max(i, m.getSeconds())) } } A(t, a, i) } } function F(e) { var n = e || w.latestSelectedDateObj; n && n instanceof Date && A(n.getHours(), n.getMinutes(), n.getSeconds()) } function A(e, n, t) { void 0 !== w.latestSelectedDateObj && w.latestSelectedDateObj.setHours(e % 24, n, t || 0, 0), w.hourElement && w.minuteElement && !w.isMobile && (w.hourElement.value = o(w.config.time_24hr ? e : (12 + e) % 12 + 12 * r(e % 12 == 0)), w.minuteElement.value = o(n), void 0 !== w.amPM && (w.amPM.textContent = w.l10n.amPM[r(e >= 12)]), void 0 !== w.secondElement && (w.secondElement.value = o(t))) } function N(e) { var n = g(e), t = parseInt(n.value) + (e.delta || 0); (t / 1e3 > 1 || "Enter" === e.key && !/[^\d]/.test(t.toString())) && ee(t) } function P(e, n, t, a) { return n instanceof Array ? n.forEach((function (n) { return P(e, n, t, a) })) : e instanceof Array ? e.forEach((function (e) { return P(e, n, t, a) })) : (e.addEventListener(n, t, a), void w._handlers.push({ remove: function () { return e.removeEventListener(n, t, a) } })) } function Y() { De("onChange") } function j(e, n) { var t = void 0 !== e ? w.parseDate(e) : w.latestSelectedDateObj || (w.config.minDate && w.config.minDate > w.now ? w.config.minDate : w.config.maxDate && w.config.maxDate < w.now ? w.config.maxDate : w.now), a = w.currentYear, i = w.currentMonth; try { void 0 !== t && (w.currentYear = t.getFullYear(), w.currentMonth = t.getMonth()) } catch (e) { e.message = "Invalid date supplied: " + t, w.config.errorHandler(e) } n && w.currentYear !== a && (De("onYearChange"), q()), !n || w.currentYear === a && w.currentMonth === i || De("onMonthChange"), w.redraw() } function H(e) { var n = g(e); ~n.className.indexOf("arrow") && L(e, n.classList.contains("arrowUp") ? 1 : -1) } function L(e, n, t) { var a = e && g(e), i = t || a && a.parentNode && a.parentNode.firstChild, o = we("increment"); o.delta = n, i && i.dispatchEvent(o) } function R(e, n, t, a) { var i = ne(n, !0), o = d("span", e, n.getDate().toString()); return o.dateObj = n, o.$i = a, o.setAttribute("aria-label", w.formatDate(n, w.config.ariaDateFormat)), -1 === e.indexOf("hidden") && 0 === M(n, w.now) && (w.todayDateElem = o, o.classList.add("today"), o.setAttribute("aria-current", "date")), i ? (o.tabIndex = -1, be(n) && (o.classList.add("selected"), w.selectedDateElem = o, "range" === w.config.mode && (s(o, "startRange", w.selectedDates[0] && 0 === M(n, w.selectedDates[0], !0)), s(o, "endRange", w.selectedDates[1] && 0 === M(n, w.selectedDates[1], !0)), "nextMonthDay" === e && o.classList.add("inRange")))) : o.classList.add("flatpickr-disabled"), "range" === w.config.mode && function (e) { return !("range" !== w.config.mode || w.selectedDates.length < 2) && (M(e, w.selectedDates[0]) >= 0 && M(e, w.selectedDates[1]) <= 0) }(n) && !be(n) && o.classList.add("inRange"), w.weekNumbers && 1 === w.config.showMonths && "prevMonthDay" !== e && a % 7 == 6 && w.weekNumbers.insertAdjacentHTML("beforeend", "<span class='flatpickr-day'>" + w.config.getWeek(n) + "</span>"), De("onDayCreate", o), o } function W(e) { e.focus(), "range" === w.config.mode && oe(e) } function B(e) { for (var n = e > 0 ? 0 : w.config.showMonths - 1, t = e > 0 ? w.config.showMonths : -1, a = n; a != t; a += e)for (var i = w.daysContainer.children[a], o = e > 0 ? 0 : i.children.length - 1, r = e > 0 ? i.children.length : -1, l = o; l != r; l += e) { var c = i.children[l]; if (-1 === c.className.indexOf("hidden") && ne(c.dateObj)) return c } } function J(e, n) { var t = k(), a = te(t || document.body), i = void 0 !== e ? e : a ? t : void 0 !== w.selectedDateElem && te(w.selectedDateElem) ? w.selectedDateElem : void 0 !== w.todayDateElem && te(w.todayDateElem) ? w.todayDateElem : B(n > 0 ? 1 : -1); void 0 === i ? w._input.focus() : a ? function (e, n) { for (var t = -1 === e.className.indexOf("Month") ? e.dateObj.getMonth() : w.currentMonth, a = n > 0 ? w.config.showMonths : -1, i = n > 0 ? 1 : -1, o = t - w.currentMonth; o != a; o += i)for (var r = w.daysContainer.children[o], l = t - w.currentMonth === o ? e.$i + n : n < 0 ? r.children.length - 1 : 0, c = r.children.length, s = l; s >= 0 && s < c && s != (n > 0 ? c : -1); s += i) { var d = r.children[s]; if (-1 === d.className.indexOf("hidden") && ne(d.dateObj) && Math.abs(e.$i - s) >= Math.abs(n)) return W(d) } w.changeMonth(i), J(B(i), 0) }(i, n) : W(i) } function K(e, n) { for (var t = (new Date(e, n, 1).getDay() - w.l10n.firstDayOfWeek + 7) % 7, a = w.utils.getDaysInMonth((n - 1 + 12) % 12, e), i = w.utils.getDaysInMonth(n, e), o = window.document.createDocumentFragment(), r = w.config.showMonths > 1, l = r ? "prevMonthDay hidden" : "prevMonthDay", c = r ? "nextMonthDay hidden" : "nextMonthDay", s = a + 1 - t, u = 0; s <= a; s++, u++)o.appendChild(R("flatpickr-day " + l, new Date(e, n - 1, s), 0, u)); for (s = 1; s <= i; s++, u++)o.appendChild(R("flatpickr-day", new Date(e, n, s), 0, u)); for (var f = i + 1; f <= 42 - t && (1 === w.config.showMonths || u % 7 != 0); f++, u++)o.appendChild(R("flatpickr-day " + c, new Date(e, n + 1, f % i), 0, u)); var m = d("div", "dayContainer"); return m.appendChild(o), m } function U() { if (void 0 !== w.daysContainer) { u(w.daysContainer), w.weekNumbers && u(w.weekNumbers); for (var e = document.createDocumentFragment(), n = 0; n < w.config.showMonths; n++) { var t = new Date(w.currentYear, w.currentMonth, 1); t.setMonth(w.currentMonth + n), e.appendChild(K(t.getFullYear(), t.getMonth())) } w.daysContainer.appendChild(e), w.days = w.daysContainer.firstChild, "range" === w.config.mode && 1 === w.selectedDates.length && oe() } } function q() { if (!(w.config.showMonths > 1 || "dropdown" !== w.config.monthSelectorType)) { var e = function (e) { return !(void 0 !== w.config.minDate && w.currentYear === w.config.minDate.getFullYear() && e < w.config.minDate.getMonth()) && !(void 0 !== w.config.maxDate && w.currentYear === w.config.maxDate.getFullYear() && e > w.config.maxDate.getMonth()) }; w.monthsDropdownContainer.tabIndex = -1, w.monthsDropdownContainer.innerHTML = ""; for (var n = 0; n < 12; n++)if (e(n)) { var t = d("option", "flatpickr-monthDropdown-month"); t.value = new Date(w.currentYear, n).getMonth().toString(), t.textContent = h(n, w.config.shorthandCurrentMonth, w.l10n), t.tabIndex = -1, w.currentMonth === n && (t.selected = !0), w.monthsDropdownContainer.appendChild(t) } } } function $() { var e, n = d("div", "flatpickr-month"), t = window.document.createDocumentFragment(); w.config.showMonths > 1 || "static" === w.config.monthSelectorType ? e = d("span", "cur-month") : (w.monthsDropdownContainer = d("select", "flatpickr-monthDropdown-months"), w.monthsDropdownContainer.setAttribute("aria-label", w.l10n.monthAriaLabel), P(w.monthsDropdownContainer, "change", (function (e) { var n = g(e), t = parseInt(n.value, 10); w.changeMonth(t - w.currentMonth), De("onMonthChange") })), q(), e = w.monthsDropdownContainer); var a = m("cur-year", { tabindex: "-1" }), i = a.getElementsByTagName("input")[0]; i.setAttribute("aria-label", w.l10n.yearAriaLabel), w.config.minDate && i.setAttribute("min", w.config.minDate.getFullYear().toString()), w.config.maxDate && (i.setAttribute("max", w.config.maxDate.getFullYear().toString()), i.disabled = !!w.config.minDate && w.config.minDate.getFullYear() === w.config.maxDate.getFullYear()); var o = d("div", "flatpickr-current-month"); return o.appendChild(e), o.appendChild(a), t.appendChild(o), n.appendChild(t), { container: n, yearElement: i, monthElement: e } } function V() { u(w.monthNav), w.monthNav.appendChild(w.prevMonthNav), w.config.showMonths && (w.yearElements = [], w.monthElements = []); for (var e = w.config.showMonths; e--;) { var n = $(); w.yearElements.push(n.yearElement), w.monthElements.push(n.monthElement), w.monthNav.appendChild(n.container) } w.monthNav.appendChild(w.nextMonthNav) } function z() { w.weekdayContainer ? u(w.weekdayContainer) : w.weekdayContainer = d("div", "flatpickr-weekdays"); for (var e = w.config.showMonths; e--;) { var n = d("div", "flatpickr-weekdaycontainer"); w.weekdayContainer.appendChild(n) } return G(), w.weekdayContainer } function G() { if (w.weekdayContainer) { var e = w.l10n.firstDayOfWeek, t = n(w.l10n.weekdays.shorthand); e > 0 && e < t.length && (t = n(t.splice(e, t.length), t.splice(0, e))); for (var a = w.config.showMonths; a--;)w.weekdayContainer.children[a].innerHTML = "\n      <span class='flatpickr-weekday'>\n        " + t.join("</span><span class='flatpickr-weekday'>") + "\n      </span>\n      " } } function Z(e, n) { void 0 === n && (n = !0); var t = n ? e : e - w.currentMonth; t < 0 && !0 === w._hidePrevMonthArrow || t > 0 && !0 === w._hideNextMonthArrow || (w.currentMonth += t, (w.currentMonth < 0 || w.currentMonth > 11) && (w.currentYear += w.currentMonth > 11 ? 1 : -1, w.currentMonth = (w.currentMonth + 12) % 12, De("onYearChange"), q()), U(), De("onMonthChange"), Ce()) } function Q(e) { return w.calendarContainer.contains(e) } function X(e) { if (w.isOpen && !w.config.inline) { var n = g(e), t = Q(n), a = !(n === w.input || n === w.altInput || w.element.contains(n) || e.path && e.path.indexOf && (~e.path.indexOf(w.input) || ~e.path.indexOf(w.altInput))) && !t && !Q(e.relatedTarget), i = !w.config.ignoredFocusElements.some((function (e) { return e.contains(n) })); a && i && (w.config.allowInput && w.setDate(w._input.value, !1, w.config.altInput ? w.config.altFormat : w.config.dateFormat), void 0 !== w.timeContainer && void 0 !== w.minuteElement && void 0 !== w.hourElement && "" !== w.input.value && void 0 !== w.input.value && _(), w.close(), w.config && "range" === w.config.mode && 1 === w.selectedDates.length && w.clear(!1)) } } function ee(e) { if (!(!e || w.config.minDate && e < w.config.minDate.getFullYear() || w.config.maxDate && e > w.config.maxDate.getFullYear())) { var n = e, t = w.currentYear !== n; w.currentYear = n || w.currentYear, w.config.maxDate && w.currentYear === w.config.maxDate.getFullYear() ? w.currentMonth = Math.min(w.config.maxDate.getMonth(), w.currentMonth) : w.config.minDate && w.currentYear === w.config.minDate.getFullYear() && (w.currentMonth = Math.max(w.config.minDate.getMonth(), w.currentMonth)), t && (w.redraw(), De("onYearChange"), q()) } } function ne(e, n) { var t; void 0 === n && (n = !0); var a = w.parseDate(e, void 0, n); if (w.config.minDate && a && M(a, w.config.minDate, void 0 !== n ? n : !w.minDateHasTime) < 0 || w.config.maxDate && a && M(a, w.config.maxDate, void 0 !== n ? n : !w.maxDateHasTime) > 0) return !1; if (!w.config.enable && 0 === w.config.disable.length) return !0; if (void 0 === a) return !1; for (var i = !!w.config.enable, o = null !== (t = w.config.enable) && void 0 !== t ? t : w.config.disable, r = 0, l = void 0; r < o.length; r++) { if ("function" == typeof (l = o[r]) && l(a)) return i; if (l instanceof Date && void 0 !== a && l.getTime() === a.getTime()) return i; if ("string" == typeof l) { var c = w.parseDate(l, void 0, !0); return c && c.getTime() === a.getTime() ? i : !i } if ("object" == typeof l && void 0 !== a && l.from && l.to && a.getTime() >= l.from.getTime() && a.getTime() <= l.to.getTime()) return i } return !i } function te(e) { return void 0 !== w.daysContainer && (-1 === e.className.indexOf("hidden") && -1 === e.className.indexOf("flatpickr-disabled") && w.daysContainer.contains(e)) } function ae(e) { var n = e.target === w._input, t = w._input.value.trimEnd() !== Me(); !n || !t || e.relatedTarget && Q(e.relatedTarget) || w.setDate(w._input.value, !0, e.target === w.altInput ? w.config.altFormat : w.config.dateFormat) } function ie(e) { var n = g(e), t = w.config.wrap ? p.contains(n) : n === w._input, a = w.config.allowInput, i = w.isOpen && (!a || !t), o = w.config.inline && t && !a; if (13 === e.keyCode && t) { if (a) return w.setDate(w._input.value, !0, n === w.altInput ? w.config.altFormat : w.config.dateFormat), w.close(), n.blur(); w.open() } else if (Q(n) || i || o) { var r = !!w.timeContainer && w.timeContainer.contains(n); switch (e.keyCode) { case 13: r ? (e.preventDefault(), _(), fe()) : me(e); break; case 27: e.preventDefault(), fe(); break; case 8: case 46: t && !w.config.allowInput && (e.preventDefault(), w.clear()); break; case 37: case 39: if (r || t) w.hourElement && w.hourElement.focus(); else { e.preventDefault(); var l = k(); if (void 0 !== w.daysContainer && (!1 === a || l && te(l))) { var c = 39 === e.keyCode ? 1 : -1; e.ctrlKey ? (e.stopPropagation(), Z(c), J(B(1), 0)) : J(void 0, c) } } break; case 38: case 40: e.preventDefault(); var s = 40 === e.keyCode ? 1 : -1; w.daysContainer && void 0 !== n.$i || n === w.input || n === w.altInput ? e.ctrlKey ? (e.stopPropagation(), ee(w.currentYear - s), J(B(1), 0)) : r || J(void 0, 7 * s) : n === w.currentYearElement ? ee(w.currentYear - s) : w.config.enableTime && (!r && w.hourElement && w.hourElement.focus(), _(e), w._debouncedChange()); break; case 9: if (r) { var d = [w.hourElement, w.minuteElement, w.secondElement, w.amPM].concat(w.pluginElements).filter((function (e) { return e })), u = d.indexOf(n); if (-1 !== u) { var f = d[u + (e.shiftKey ? -1 : 1)]; e.preventDefault(), (f || w._input).focus() } } else !w.config.noCalendar && w.daysContainer && w.daysContainer.contains(n) && e.shiftKey && (e.preventDefault(), w._input.focus()) } } if (void 0 !== w.amPM && n === w.amPM) switch (e.key) { case w.l10n.amPM[0].charAt(0): case w.l10n.amPM[0].charAt(0).toLowerCase(): w.amPM.textContent = w.l10n.amPM[0], O(), ye(); break; case w.l10n.amPM[1].charAt(0): case w.l10n.amPM[1].charAt(0).toLowerCase(): w.amPM.textContent = w.l10n.amPM[1], O(), ye() }(t || Q(n)) && De("onKeyDown", e) } function oe(e, n) { if (void 0 === n && (n = "flatpickr-day"), 1 === w.selectedDates.length && (!e || e.classList.contains(n) && !e.classList.contains("flatpickr-disabled"))) { for (var t = e ? e.dateObj.getTime() : w.days.firstElementChild.dateObj.getTime(), a = w.parseDate(w.selectedDates[0], void 0, !0).getTime(), i = Math.min(t, w.selectedDates[0].getTime()), o = Math.max(t, w.selectedDates[0].getTime()), r = !1, l = 0, c = 0, s = i; s < o; s += x)ne(new Date(s), !0) || (r = r || s > i && s < o, s < a && (!l || s > l) ? l = s : s > a && (!c || s < c) && (c = s)); Array.from(w.rContainer.querySelectorAll("*:nth-child(-n+" + w.config.showMonths + ") > ." + n)).forEach((function (n) { var i, o, s, d = n.dateObj.getTime(), u = l > 0 && d < l || c > 0 && d > c; if (u) return n.classList.add("notAllowed"), void ["inRange", "startRange", "endRange"].forEach((function (e) { n.classList.remove(e) })); r && !u || (["startRange", "inRange", "endRange", "notAllowed"].forEach((function (e) { n.classList.remove(e) })), void 0 !== e && (e.classList.add(t <= w.selectedDates[0].getTime() ? "startRange" : "endRange"), a < t && d === a ? n.classList.add("startRange") : a > t && d === a && n.classList.add("endRange"), d >= l && (0 === c || d <= c) && (o = a, s = t, (i = d) > Math.min(o, s) && i < Math.max(o, s)) && n.classList.add("inRange"))) })) } } function re() { !w.isOpen || w.config.static || w.config.inline || de() } function le(e) { return function (n) { var t = w.config["_" + e + "Date"] = w.parseDate(n, w.config.dateFormat), a = w.config["_" + ("min" === e ? "max" : "min") + "Date"]; void 0 !== t && (w["min" === e ? "minDateHasTime" : "maxDateHasTime"] = t.getHours() > 0 || t.getMinutes() > 0 || t.getSeconds() > 0), w.selectedDates && (w.selectedDates = w.selectedDates.filter((function (e) { return ne(e) })), w.selectedDates.length || "min" !== e || F(t), ye()), w.daysContainer && (ue(), void 0 !== t ? w.currentYearElement[e] = t.getFullYear().toString() : w.currentYearElement.removeAttribute(e), w.currentYearElement.disabled = !!a && void 0 !== t && a.getFullYear() === t.getFullYear()) } } function ce() { return w.config.wrap ? p.querySelector("[data-input]") : p } function se() { "object" != typeof w.config.locale && void 0 === I.l10ns[w.config.locale] && w.config.errorHandler(new Error("flatpickr: invalid locale " + w.config.locale)), w.l10n = e(e({}, I.l10ns.default), "object" == typeof w.config.locale ? w.config.locale : "default" !== w.config.locale ? I.l10ns[w.config.locale] : void 0), D.D = "(" + w.l10n.weekdays.shorthand.join("|") + ")", D.l = "(" + w.l10n.weekdays.longhand.join("|") + ")", D.M = "(" + w.l10n.months.shorthand.join("|") + ")", D.F = "(" + w.l10n.months.longhand.join("|") + ")", D.K = "(" + w.l10n.amPM[0] + "|" + w.l10n.amPM[1] + "|" + w.l10n.amPM[0].toLowerCase() + "|" + w.l10n.amPM[1].toLowerCase() + ")", void 0 === e(e({}, v), JSON.parse(JSON.stringify(p.dataset || {}))).time_24hr && void 0 === I.defaultConfig.time_24hr && (w.config.time_24hr = w.l10n.time_24hr), w.formatDate = b(w), w.parseDate = C({ config: w.config, l10n: w.l10n }) } function de(e) { if ("function" != typeof w.config.position) { if (void 0 !== w.calendarContainer) { De("onPreCalendarPosition"); var n = e || w._positionElement, t = Array.prototype.reduce.call(w.calendarContainer.children, (function (e, n) { return e + n.offsetHeight }), 0), a = w.calendarContainer.offsetWidth, i = w.config.position.split(" "), o = i[0], r = i.length > 1 ? i[1] : null, l = n.getBoundingClientRect(), c = window.innerHeight - l.bottom, d = "above" === o || "below" !== o && c < t && l.top > t, u = window.pageYOffset + l.top + (d ? -t - 2 : n.offsetHeight + 2); if (s(w.calendarContainer, "arrowTop", !d), s(w.calendarContainer, "arrowBottom", d), !w.config.inline) { var f = window.pageXOffset + l.left, m = !1, g = !1; "center" === r ? (f -= (a - l.width) / 2, m = !0) : "right" === r && (f -= a - l.width, g = !0), s(w.calendarContainer, "arrowLeft", !m && !g), s(w.calendarContainer, "arrowCenter", m), s(w.calendarContainer, "arrowRight", g); var p = window.document.body.offsetWidth - (window.pageXOffset + l.right), h = f + a > window.document.body.offsetWidth, v = p + a > window.document.body.offsetWidth; if (s(w.calendarContainer, "rightMost", h), !w.config.static) if (w.calendarContainer.style.top = u + "px", h) if (v) { var D = function () { for (var e = null, n = 0; n < document.styleSheets.length; n++) { var t = document.styleSheets[n]; if (t.cssRules) { try { t.cssRules } catch (e) { continue } e = t; break } } return null != e ? e : (a = document.createElement("style"), document.head.appendChild(a), a.sheet); var a }(); if (void 0 === D) return; var b = window.document.body.offsetWidth, C = Math.max(0, b / 2 - a / 2), M = D.cssRules.length, y = "{left:" + l.left + "px;right:auto;}"; s(w.calendarContainer, "rightMost", !1), s(w.calendarContainer, "centerMost", !0), D.insertRule(".flatpickr-calendar.centerMost:before,.flatpickr-calendar.centerMost:after" + y, M), w.calendarContainer.style.left = C + "px", w.calendarContainer.style.right = "auto" } else w.calendarContainer.style.left = "auto", w.calendarContainer.style.right = p + "px"; else w.calendarContainer.style.left = f + "px", w.calendarContainer.style.right = "auto" } } } else w.config.position(w, e) } function ue() { w.config.noCalendar || w.isMobile || (q(), Ce(), U()) } function fe() { w._input.focus(), -1 !== window.navigator.userAgent.indexOf("MSIE") || void 0 !== navigator.msMaxTouchPoints ? setTimeout(w.close, 0) : w.close() } function me(e) { e.preventDefault(), e.stopPropagation(); var n = f(g(e), (function (e) { return e.classList && e.classList.contains("flatpickr-day") && !e.classList.contains("flatpickr-disabled") && !e.classList.contains("notAllowed") })); if (void 0 !== n) { var t = n, a = w.latestSelectedDateObj = new Date(t.dateObj.getTime()), i = (a.getMonth() < w.currentMonth || a.getMonth() > w.currentMonth + w.config.showMonths - 1) && "range" !== w.config.mode; if (w.selectedDateElem = t, "single" === w.config.mode) w.selectedDates = [a]; else if ("multiple" === w.config.mode) { var o = be(a); o ? w.selectedDates.splice(parseInt(o), 1) : w.selectedDates.push(a) } else "range" === w.config.mode && (2 === w.selectedDates.length && w.clear(!1, !1), w.latestSelectedDateObj = a, w.selectedDates.push(a), 0 !== M(a, w.selectedDates[0], !0) && w.selectedDates.sort((function (e, n) { return e.getTime() - n.getTime() }))); if (O(), i) { var r = w.currentYear !== a.getFullYear(); w.currentYear = a.getFullYear(), w.currentMonth = a.getMonth(), r && (De("onYearChange"), q()), De("onMonthChange") } if (Ce(), U(), ye(), i || "range" === w.config.mode || 1 !== w.config.showMonths ? void 0 !== w.selectedDateElem && void 0 === w.hourElement && w.selectedDateElem && w.selectedDateElem.focus() : W(t), void 0 !== w.hourElement && void 0 !== w.hourElement && w.hourElement.focus(), w.config.closeOnSelect) { var l = "single" === w.config.mode && !w.config.enableTime, c = "range" === w.config.mode && 2 === w.selectedDates.length && !w.config.enableTime; (l || c) && fe() } Y() } } w.parseDate = C({ config: w.config, l10n: w.l10n }), w._handlers = [], w.pluginElements = [], w.loadedPlugins = [], w._bind = P, w._setHoursFromDate = F, w._positionCalendar = de, w.changeMonth = Z, w.changeYear = ee, w.clear = function (e, n) { void 0 === e && (e = !0); void 0 === n && (n = !0); w.input.value = "", void 0 !== w.altInput && (w.altInput.value = ""); void 0 !== w.mobileInput && (w.mobileInput.value = ""); w.selectedDates = [], w.latestSelectedDateObj = void 0, !0 === n && (w.currentYear = w._initialDate.getFullYear(), w.currentMonth = w._initialDate.getMonth()); if (!0 === w.config.enableTime) { var t = E(w.config), a = t.hours, i = t.minutes, o = t.seconds; A(a, i, o) } w.redraw(), e && De("onChange") }, w.close = function () { w.isOpen = !1, w.isMobile || (void 0 !== w.calendarContainer && w.calendarContainer.classList.remove("open"), void 0 !== w._input && w._input.classList.remove("active")); De("onClose") }, w.onMouseOver = oe, w._createElement = d, w.createDay = R, w.destroy = function () { void 0 !== w.config && De("onDestroy"); for (var e = w._handlers.length; e--;)w._handlers[e].remove(); if (w._handlers = [], w.mobileInput) w.mobileInput.parentNode && w.mobileInput.parentNode.removeChild(w.mobileInput), w.mobileInput = void 0; else if (w.calendarContainer && w.calendarContainer.parentNode) if (w.config.static && w.calendarContainer.parentNode) { var n = w.calendarContainer.parentNode; if (n.lastChild && n.removeChild(n.lastChild), n.parentNode) { for (; n.firstChild;)n.parentNode.insertBefore(n.firstChild, n); n.parentNode.removeChild(n) } } else w.calendarContainer.parentNode.removeChild(w.calendarContainer); w.altInput && (w.input.type = "text", w.altInput.parentNode && w.altInput.parentNode.removeChild(w.altInput), delete w.altInput); w.input && (w.input.type = w.input._type, w.input.classList.remove("flatpickr-input"), w.input.removeAttribute("readonly"));["_showTimeInput", "latestSelectedDateObj", "_hideNextMonthArrow", "_hidePrevMonthArrow", "__hideNextMonthArrow", "__hidePrevMonthArrow", "isMobile", "isOpen", "selectedDateElem", "minDateHasTime", "maxDateHasTime", "days", "daysContainer", "_input", "_positionElement", "innerContainer", "rContainer", "monthNav", "todayDateElem", "calendarContainer", "weekdayContainer", "prevMonthNav", "nextMonthNav", "monthsDropdownContainer", "currentMonthElement", "currentYearElement", "navigationCurrentMonth", "selectedDateElem", "config"].forEach((function (e) { try { delete w[e] } catch (e) { } })) }, w.isEnabled = ne, w.jumpToDate = j, w.updateValue = ye, w.open = function (e, n) { void 0 === n && (n = w._positionElement); if (!0 === w.isMobile) { if (e) { e.preventDefault(); var t = g(e); t && t.blur() } return void 0 !== w.mobileInput && (w.mobileInput.focus(), w.mobileInput.click()), void De("onOpen") } if (w._input.disabled || w.config.inline) return; var a = w.isOpen; w.isOpen = !0, a || (w.calendarContainer.classList.add("open"), w._input.classList.add("active"), De("onOpen"), de(n)); !0 === w.config.enableTime && !0 === w.config.noCalendar && (!1 !== w.config.allowInput || void 0 !== e && w.timeContainer.contains(e.relatedTarget) || setTimeout((function () { return w.hourElement.select() }), 50)) }, w.redraw = ue, w.set = function (e, n) { if (null !== e && "object" == typeof e) for (var a in Object.assign(w.config, e), e) void 0 !== ge[a] && ge[a].forEach((function (e) { return e() })); else w.config[e] = n, void 0 !== ge[e] ? ge[e].forEach((function (e) { return e() })) : t.indexOf(e) > -1 && (w.config[e] = c(n)); w.redraw(), ye(!0) }, w.setDate = function (e, n, t) { void 0 === n && (n = !1); void 0 === t && (t = w.config.dateFormat); if (0 !== e && !e || e instanceof Array && 0 === e.length) return w.clear(n); pe(e, t), w.latestSelectedDateObj = w.selectedDates[w.selectedDates.length - 1], w.redraw(), j(void 0, n), F(), 0 === w.selectedDates.length && w.clear(!1); ye(n), n && De("onChange") }, w.toggle = function (e) { if (!0 === w.isOpen) return w.close(); w.open(e) }; var ge = { locale: [se, G], showMonths: [V, S, z], minDate: [j], maxDate: [j], positionElement: [ve], clickOpens: [function () { !0 === w.config.clickOpens ? (P(w._input, "focus", w.open), P(w._input, "click", w.open)) : (w._input.removeEventListener("focus", w.open), w._input.removeEventListener("click", w.open)) }] }; function pe(e, n) { var t = []; if (e instanceof Array) t = e.map((function (e) { return w.parseDate(e, n) })); else if (e instanceof Date || "number" == typeof e) t = [w.parseDate(e, n)]; else if ("string" == typeof e) switch (w.config.mode) { case "single": case "time": t = [w.parseDate(e, n)]; break; case "multiple": t = e.split(w.config.conjunction).map((function (e) { return w.parseDate(e, n) })); break; case "range": t = e.split(w.l10n.rangeSeparator).map((function (e) { return w.parseDate(e, n) })) } else w.config.errorHandler(new Error("Invalid date supplied: " + JSON.stringify(e))); w.selectedDates = w.config.allowInvalidPreload ? t : t.filter((function (e) { return e instanceof Date && ne(e, !1) })), "range" === w.config.mode && w.selectedDates.sort((function (e, n) { return e.getTime() - n.getTime() })) } function he(e) { return e.slice().map((function (e) { return "string" == typeof e || "number" == typeof e || e instanceof Date ? w.parseDate(e, void 0, !0) : e && "object" == typeof e && e.from && e.to ? { from: w.parseDate(e.from, void 0), to: w.parseDate(e.to, void 0) } : e })).filter((function (e) { return e })) } function ve() { w._positionElement = w.config.positionElement || w._input } function De(e, n) { if (void 0 !== w.config) { var t = w.config[e]; if (void 0 !== t && t.length > 0) for (var a = 0; t[a] && a < t.length; a++)t[a](w.selectedDates, w.input.value, w, n); "onChange" === e && (w.input.dispatchEvent(we("change")), w.input.dispatchEvent(we("input"))) } } function we(e) { var n = document.createEvent("Event"); return n.initEvent(e, !0, !0), n } function be(e) { for (var n = 0; n < w.selectedDates.length; n++) { var t = w.selectedDates[n]; if (t instanceof Date && 0 === M(t, e)) return "" + n } return !1 } function Ce() { w.config.noCalendar || w.isMobile || !w.monthNav || (w.yearElements.forEach((function (e, n) { var t = new Date(w.currentYear, w.currentMonth, 1); t.setMonth(w.currentMonth + n), w.config.showMonths > 1 || "static" === w.config.monthSelectorType ? w.monthElements[n].textContent = h(t.getMonth(), w.config.shorthandCurrentMonth, w.l10n) + " " : w.monthsDropdownContainer.value = t.getMonth().toString(), e.value = t.getFullYear().toString() })), w._hidePrevMonthArrow = void 0 !== w.config.minDate && (w.currentYear === w.config.minDate.getFullYear() ? w.currentMonth <= w.config.minDate.getMonth() : w.currentYear < w.config.minDate.getFullYear()), w._hideNextMonthArrow = void 0 !== w.config.maxDate && (w.currentYear === w.config.maxDate.getFullYear() ? w.currentMonth + 1 > w.config.maxDate.getMonth() : w.currentYear > w.config.maxDate.getFullYear())) } function Me(e) { var n = e || (w.config.altInput ? w.config.altFormat : w.config.dateFormat); return w.selectedDates.map((function (e) { return w.formatDate(e, n) })).filter((function (e, n, t) { return "range" !== w.config.mode || w.config.enableTime || t.indexOf(e) === n })).join("range" !== w.config.mode ? w.config.conjunction : w.l10n.rangeSeparator) } function ye(e) { void 0 === e && (e = !0), void 0 !== w.mobileInput && w.mobileFormatStr && (w.mobileInput.value = void 0 !== w.latestSelectedDateObj ? w.formatDate(w.latestSelectedDateObj, w.mobileFormatStr) : ""), w.input.value = Me(w.config.dateFormat), void 0 !== w.altInput && (w.altInput.value = Me(w.config.altFormat)), !1 !== e && De("onValueUpdate") } function xe(e) { var n = g(e), t = w.prevMonthNav.contains(n), a = w.nextMonthNav.contains(n); t || a ? Z(t ? -1 : 1) : w.yearElements.indexOf(n) >= 0 ? n.select() : n.classList.contains("arrowUp") ? w.changeYear(w.currentYear + 1) : n.classList.contains("arrowDown") && w.changeYear(w.currentYear - 1) } return function () { w.element = w.input = p, w.isOpen = !1, function () { var n = ["wrap", "weekNumbers", "allowInput", "allowInvalidPreload", "clickOpens", "time_24hr", "enableTime", "noCalendar", "altInput", "shorthandCurrentMonth", "inline", "static", "enableSeconds", "disableMobile"], i = e(e({}, JSON.parse(JSON.stringify(p.dataset || {}))), v), o = {}; w.config.parseDate = i.parseDate, w.config.formatDate = i.formatDate, Object.defineProperty(w.config, "enable", { get: function () { return w.config._enable }, set: function (e) { w.config._enable = he(e) } }), Object.defineProperty(w.config, "disable", { get: function () { return w.config._disable }, set: function (e) { w.config._disable = he(e) } }); var r = "time" === i.mode; if (!i.dateFormat && (i.enableTime || r)) { var l = I.defaultConfig.dateFormat || a.dateFormat; o.dateFormat = i.noCalendar || r ? "H:i" + (i.enableSeconds ? ":S" : "") : l + " H:i" + (i.enableSeconds ? ":S" : "") } if (i.altInput && (i.enableTime || r) && !i.altFormat) { var s = I.defaultConfig.altFormat || a.altFormat; o.altFormat = i.noCalendar || r ? "h:i" + (i.enableSeconds ? ":S K" : " K") : s + " h:i" + (i.enableSeconds ? ":S" : "") + " K" } Object.defineProperty(w.config, "minDate", { get: function () { return w.config._minDate }, set: le("min") }), Object.defineProperty(w.config, "maxDate", { get: function () { return w.config._maxDate }, set: le("max") }); var d = function (e) { return function (n) { w.config["min" === e ? "_minTime" : "_maxTime"] = w.parseDate(n, "H:i:S") } }; Object.defineProperty(w.config, "minTime", { get: function () { return w.config._minTime }, set: d("min") }), Object.defineProperty(w.config, "maxTime", { get: function () { return w.config._maxTime }, set: d("max") }), "time" === i.mode && (w.config.noCalendar = !0, w.config.enableTime = !0); Object.assign(w.config, o, i); for (var u = 0; u < n.length; u++)w.config[n[u]] = !0 === w.config[n[u]] || "true" === w.config[n[u]]; t.filter((function (e) { return void 0 !== w.config[e] })).forEach((function (e) { w.config[e] = c(w.config[e] || []).map(T) })), w.isMobile = !w.config.disableMobile && !w.config.inline && "single" === w.config.mode && !w.config.disable.length && !w.config.enable && !w.config.weekNumbers && /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent); for (u = 0; u < w.config.plugins.length; u++) { var f = w.config.plugins[u](w) || {}; for (var m in f) t.indexOf(m) > -1 ? w.config[m] = c(f[m]).map(T).concat(w.config[m]) : void 0 === i[m] && (w.config[m] = f[m]) } i.altInputClass || (w.config.altInputClass = ce().className + " " + w.config.altInputClass); De("onParseConfig") }(), se(), function () { if (w.input = ce(), !w.input) return void w.config.errorHandler(new Error("Invalid input element specified")); w.input._type = w.input.type, w.input.type = "text", w.input.classList.add("flatpickr-input"), w._input = w.input, w.config.altInput && (w.altInput = d(w.input.nodeName, w.config.altInputClass), w._input = w.altInput, w.altInput.placeholder = w.input.placeholder, w.altInput.disabled = w.input.disabled, w.altInput.required = w.input.required, w.altInput.tabIndex = w.input.tabIndex, w.altInput.type = "text", w.input.setAttribute("type", "hidden"), !w.config.static && w.input.parentNode && w.input.parentNode.insertBefore(w.altInput, w.input.nextSibling)); w.config.allowInput || w._input.setAttribute("readonly", "readonly"); ve() }(), function () { w.selectedDates = [], w.now = w.parseDate(w.config.now) || new Date; var e = w.config.defaultDate || ("INPUT" !== w.input.nodeName && "TEXTAREA" !== w.input.nodeName || !w.input.placeholder || w.input.value !== w.input.placeholder ? w.input.value : null); e && pe(e, w.config.dateFormat); w._initialDate = w.selectedDates.length > 0 ? w.selectedDates[0] : w.config.minDate && w.config.minDate.getTime() > w.now.getTime() ? w.config.minDate : w.config.maxDate && w.config.maxDate.getTime() < w.now.getTime() ? w.config.maxDate : w.now, w.currentYear = w._initialDate.getFullYear(), w.currentMonth = w._initialDate.getMonth(), w.selectedDates.length > 0 && (w.latestSelectedDateObj = w.selectedDates[0]); void 0 !== w.config.minTime && (w.config.minTime = w.parseDate(w.config.minTime, "H:i")); void 0 !== w.config.maxTime && (w.config.maxTime = w.parseDate(w.config.maxTime, "H:i")); w.minDateHasTime = !!w.config.minDate && (w.config.minDate.getHours() > 0 || w.config.minDate.getMinutes() > 0 || w.config.minDate.getSeconds() > 0), w.maxDateHasTime = !!w.config.maxDate && (w.config.maxDate.getHours() > 0 || w.config.maxDate.getMinutes() > 0 || w.config.maxDate.getSeconds() > 0) }(), w.utils = { getDaysInMonth: function (e, n) { return void 0 === e && (e = w.currentMonth), void 0 === n && (n = w.currentYear), 1 === e && (n % 4 == 0 && n % 100 != 0 || n % 400 == 0) ? 29 : w.l10n.daysInMonth[e] } }, w.isMobile || function () { var e = window.document.createDocumentFragment(); if (w.calendarContainer = d("div", "flatpickr-calendar"), w.calendarContainer.tabIndex = -1, !w.config.noCalendar) { if (e.appendChild((w.monthNav = d("div", "flatpickr-months"), w.yearElements = [], w.monthElements = [], w.prevMonthNav = d("span", "flatpickr-prev-month"), w.prevMonthNav.innerHTML = w.config.prevArrow, w.nextMonthNav = d("span", "flatpickr-next-month"), w.nextMonthNav.innerHTML = w.config.nextArrow, V(), Object.defineProperty(w, "_hidePrevMonthArrow", { get: function () { return w.__hidePrevMonthArrow }, set: function (e) { w.__hidePrevMonthArrow !== e && (s(w.prevMonthNav, "flatpickr-disabled", e), w.__hidePrevMonthArrow = e) } }), Object.defineProperty(w, "_hideNextMonthArrow", { get: function () { return w.__hideNextMonthArrow }, set: function (e) { w.__hideNextMonthArrow !== e && (s(w.nextMonthNav, "flatpickr-disabled", e), w.__hideNextMonthArrow = e) } }), w.currentYearElement = w.yearElements[0], Ce(), w.monthNav)), w.innerContainer = d("div", "flatpickr-innerContainer"), w.config.weekNumbers) { var n = function () { w.calendarContainer.classList.add("hasWeeks"); var e = d("div", "flatpickr-weekwrapper"); e.appendChild(d("span", "flatpickr-weekday", w.l10n.weekAbbreviation)); var n = d("div", "flatpickr-weeks"); return e.appendChild(n), { weekWrapper: e, weekNumbers: n } }(), t = n.weekWrapper, a = n.weekNumbers; w.innerContainer.appendChild(t), w.weekNumbers = a, w.weekWrapper = t } w.rContainer = d("div", "flatpickr-rContainer"), w.rContainer.appendChild(z()), w.daysContainer || (w.daysContainer = d("div", "flatpickr-days"), w.daysContainer.tabIndex = -1), U(), w.rContainer.appendChild(w.daysContainer), w.innerContainer.appendChild(w.rContainer), e.appendChild(w.innerContainer) } w.config.enableTime && e.appendChild(function () { w.calendarContainer.classList.add("hasTime"), w.config.noCalendar && w.calendarContainer.classList.add("noCalendar"); var e = E(w.config); w.timeContainer = d("div", "flatpickr-time"), w.timeContainer.tabIndex = -1; var n = d("span", "flatpickr-time-separator", ":"), t = m("flatpickr-hour", { "aria-label": w.l10n.hourAriaLabel }); w.hourElement = t.getElementsByTagName("input")[0]; var a = m("flatpickr-minute", { "aria-label": w.l10n.minuteAriaLabel }); w.minuteElement = a.getElementsByTagName("input")[0], w.hourElement.tabIndex = w.minuteElement.tabIndex = -1, w.hourElement.value = o(w.latestSelectedDateObj ? w.latestSelectedDateObj.getHours() : w.config.time_24hr ? e.hours : function (e) { switch (e % 24) { case 0: case 12: return 12; default: return e % 12 } }(e.hours)), w.minuteElement.value = o(w.latestSelectedDateObj ? w.latestSelectedDateObj.getMinutes() : e.minutes), w.hourElement.setAttribute("step", w.config.hourIncrement.toString()), w.minuteElement.setAttribute("step", w.config.minuteIncrement.toString()), w.hourElement.setAttribute("min", w.config.time_24hr ? "0" : "1"), w.hourElement.setAttribute("max", w.config.time_24hr ? "23" : "12"), w.hourElement.setAttribute("maxlength", "2"), w.minuteElement.setAttribute("min", "0"), w.minuteElement.setAttribute("max", "59"), w.minuteElement.setAttribute("maxlength", "2"), w.timeContainer.appendChild(t), w.timeContainer.appendChild(n), w.timeContainer.appendChild(a), w.config.time_24hr && w.timeContainer.classList.add("time24hr"); if (w.config.enableSeconds) { w.timeContainer.classList.add("hasSeconds"); var i = m("flatpickr-second"); w.secondElement = i.getElementsByTagName("input")[0], w.secondElement.value = o(w.latestSelectedDateObj ? w.latestSelectedDateObj.getSeconds() : e.seconds), w.secondElement.setAttribute("step", w.minuteElement.getAttribute("step")), w.secondElement.setAttribute("min", "0"), w.secondElement.setAttribute("max", "59"), w.secondElement.setAttribute("maxlength", "2"), w.timeContainer.appendChild(d("span", "flatpickr-time-separator", ":")), w.timeContainer.appendChild(i) } w.config.time_24hr || (w.amPM = d("span", "flatpickr-am-pm", w.l10n.amPM[r((w.latestSelectedDateObj ? w.hourElement.value : w.config.defaultHour) > 11)]), w.amPM.title = w.l10n.toggleTitle, w.amPM.tabIndex = -1, w.timeContainer.appendChild(w.amPM)); return w.timeContainer }()); s(w.calendarContainer, "rangeMode", "range" === w.config.mode), s(w.calendarContainer, "animate", !0 === w.config.animate), s(w.calendarContainer, "multiMonth", w.config.showMonths > 1), w.calendarContainer.appendChild(e); var i = void 0 !== w.config.appendTo && void 0 !== w.config.appendTo.nodeType; if ((w.config.inline || w.config.static) && (w.calendarContainer.classList.add(w.config.inline ? "inline" : "static"), w.config.inline && (!i && w.element.parentNode ? w.element.parentNode.insertBefore(w.calendarContainer, w._input.nextSibling) : void 0 !== w.config.appendTo && w.config.appendTo.appendChild(w.calendarContainer)), w.config.static)) { var l = d("div", "flatpickr-wrapper"); w.element.parentNode && w.element.parentNode.insertBefore(l, w.element), l.appendChild(w.element), w.altInput && l.appendChild(w.altInput), l.appendChild(w.calendarContainer) } w.config.static || w.config.inline || (void 0 !== w.config.appendTo ? w.config.appendTo : window.document.body).appendChild(w.calendarContainer) }(), function () { w.config.wrap && ["open", "close", "toggle", "clear"].forEach((function (e) { Array.prototype.forEach.call(w.element.querySelectorAll("[data-" + e + "]"), (function (n) { return P(n, "click", w[e]) })) })); if (w.isMobile) return void function () { var e = w.config.enableTime ? w.config.noCalendar ? "time" : "datetime-local" : "date"; w.mobileInput = d("input", w.input.className + " flatpickr-mobile"), w.mobileInput.tabIndex = 1, w.mobileInput.type = e, w.mobileInput.disabled = w.input.disabled, w.mobileInput.required = w.input.required, w.mobileInput.placeholder = w.input.placeholder, w.mobileFormatStr = "datetime-local" === e ? "Y-m-d\\TH:i:S" : "date" === e ? "Y-m-d" : "H:i:S", w.selectedDates.length > 0 && (w.mobileInput.defaultValue = w.mobileInput.value = w.formatDate(w.selectedDates[0], w.mobileFormatStr)); w.config.minDate && (w.mobileInput.min = w.formatDate(w.config.minDate, "Y-m-d")); w.config.maxDate && (w.mobileInput.max = w.formatDate(w.config.maxDate, "Y-m-d")); w.input.getAttribute("step") && (w.mobileInput.step = String(w.input.getAttribute("step"))); w.input.type = "hidden", void 0 !== w.altInput && (w.altInput.type = "hidden"); try { w.input.parentNode && w.input.parentNode.insertBefore(w.mobileInput, w.input.nextSibling) } catch (e) { } P(w.mobileInput, "change", (function (e) { w.setDate(g(e).value, !1, w.mobileFormatStr), De("onChange"), De("onClose") })) }(); var e = l(re, 50); w._debouncedChange = l(Y, 300), w.daysContainer && !/iPhone|iPad|iPod/i.test(navigator.userAgent) && P(w.daysContainer, "mouseover", (function (e) { "range" === w.config.mode && oe(g(e)) })); P(w._input, "keydown", ie), void 0 !== w.calendarContainer && P(w.calendarContainer, "keydown", ie); w.config.inline || w.config.static || P(window, "resize", e); void 0 !== window.ontouchstart ? P(window.document, "touchstart", X) : P(window.document, "mousedown", X); P(window.document, "focus", X, { capture: !0 }), !0 === w.config.clickOpens && (P(w._input, "focus", w.open), P(w._input, "click", w.open)); void 0 !== w.daysContainer && (P(w.monthNav, "click", xe), P(w.monthNav, ["keyup", "increment"], N), P(w.daysContainer, "click", me)); if (void 0 !== w.timeContainer && void 0 !== w.minuteElement && void 0 !== w.hourElement) { var n = function (e) { return g(e).select() }; P(w.timeContainer, ["increment"], _), P(w.timeContainer, "blur", _, { capture: !0 }), P(w.timeContainer, "click", H), P([w.hourElement, w.minuteElement], ["focus", "click"], n), void 0 !== w.secondElement && P(w.secondElement, "focus", (function () { return w.secondElement && w.secondElement.select() })), void 0 !== w.amPM && P(w.amPM, "click", (function (e) { _(e) })) } w.config.allowInput && P(w._input, "blur", ae) }(), (w.selectedDates.length || w.config.noCalendar) && (w.config.enableTime && F(w.config.noCalendar ? w.latestSelectedDateObj : void 0), ye(!1)), S(); var n = /^((?!chrome|android).)*safari/i.test(navigator.userAgent); !w.isMobile && n && de(), De("onReady") }(), w } function T(e, n) { for (var t = Array.prototype.slice.call(e).filter((function (e) { return e instanceof HTMLElement })), a = [], i = 0; i < t.length; i++) { var o = t[i]; try { if (null !== o.getAttribute("data-fp-omit")) continue; void 0 !== o._flatpickr && (o._flatpickr.destroy(), o._flatpickr = void 0), o._flatpickr = k(o, n || {}), a.push(o._flatpickr) } catch (e) { console.error(e) } } return 1 === a.length ? a[0] : a } "undefined" != typeof HTMLElement && "undefined" != typeof HTMLCollection && "undefined" != typeof NodeList && (HTMLCollection.prototype.flatpickr = NodeList.prototype.flatpickr = function (e) { return T(this, e) }, HTMLElement.prototype.flatpickr = function (e) { return T([this], e) }); var I = function (e, n) { return "string" == typeof e ? T(window.document.querySelectorAll(e), n) : e instanceof Node ? T([e], n) : T(e, n) }; return I.defaultConfig = {}, I.l10ns = { en: e({}, i), default: e({}, i) }, I.localize = function (n) { I.l10ns.default = e(e({}, I.l10ns.default), n) }, I.setDefaults = function (n) { I.defaultConfig = e(e({}, I.defaultConfig), n) }, I.parseDate = C({}), I.formatDate = b({}), I.compareDates = M, "undefined" != typeof jQuery && void 0 !== jQuery.fn && (jQuery.fn.flatpickr = function (e) { return T(this, e) }), Date.prototype.fp_incr = function (e) { return new Date(this.getFullYear(), this.getMonth(), this.getDate() + ("string" == typeof e ? parseInt(e, 10) : e)) }, "undefined" != typeof window && (window.flatpickr = I), I }));
    </script>
    <script>
        const loader = document.getElementById("loader");
        const content = document.getElementById("content");
        const gender = ['未知', '男', '女'];
        const wxid={{wxid}};
        const isChatroom = wxid.endsWith("@chatroom");
        const avatarPaths = {{avatarPaths}};
        const avatarUrls = {{avatarUrls}};
        const chatMessages = /*注意看这是分割线*/;
        var timelineData = {{timelineData}};
        var PageTimeline = {{PageTimeline}};
        var server_id_Page = {{server_id_Page}};
        var server_id_Idx = {{server_id_Idx}};
        const dateDataMap = {{dateDataMap}};
        const AllIndex = {{AllIndex}};
        const ImageIndex = {{ImageIndex}};
        const FileIndex = {{FileIndex}};
        const LinkIndex = {{LinkIndex}};
        const MusicIndex = {{MusicIndex}};
        const TransferIndex = {{TransferIndex}};
        const MiniProgramIndex = {{MiniProgramIndex}};
        const VideoNumberIndex = {{VideoNumberIndex}};
    </script>
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            serviceHost: "https://api.memotrace.cn/_AMapService",

        };
    </script>
    <script type="text/javascript"
        src="https://webapi.amap.com/maps?v=2.0&key=b999f45a5b06a184518f744681048368"></script>
    <script>
        //     const markerContent = `<div class="custom-content-marker">
        // <img src="./icon/location.png">`
        var MapID = {};
    </script>
    <script type="text/javascript" src="http://apps.bdimg.com/libs/jquery/1.10.0/jquery.min.js"></script>
    <script>
        class MessageType {
            static Unknown = -1;
            static Text = "1";
            static Image = "3";
            static Audio = "34";
            static BusinessCard = "42";
            static Video = "43";
            static Emoji = "47";
            static Position = "48";
            static Voip = "50";
            static OpenIMBCard = "66";
            static System = "10000";
            static File = "***********"; // Use BigInt for large numbers
            static LinkMessage = 21474836529n;
            static LinkMessage2 = 292057776177n;
            static LinkMessage3 = 12884901937n;
            static LinkMessage4 = 4294967345n;
            static LinkMessage5 = 326417514545n;
            static LinkMessage6 = 17179869233n;
            static RedEnvelope = 8594229559345n;
            static Transfer = 8589934592049n;
            static Quote = 244813135921n;
            static MergedMessages = 81604378673n;
            static Applet = "141733920817";
            static Applet2 = 154618822705n;
            // static Applet3 = 17179869233n; // Uncomment if needed
            static WeChatVideo = "219043332145";
            static Music = "12884901937";
            static FavNote = 103079215153n;
            static Pat = 266287972401n;
        }

        const menuToggle = document.getElementById("menu-toggle");
        const navbarNav = document.getElementById("topnavbar-nav");

        menuToggle.addEventListener("click", () => {
            navbarNav.classList.toggle("show");
        });
    </script>
    <script>
        function renderPage(page) {
            if (ChatMsgIndex.length !== 0) {
                const currentYear = PageTimeline[page]['year'];
                const currentMonth = PageTimeline[page]['month'];
                const monthName = currentMonth < 10 ? '0' + currentMonth : currentMonth;
                // console.log(monthName+'月');
                toggleMonthsDisplay(currentYear);
                const yearIdElement = document.getElementById(currentYear + 'year');
                const monthElements = yearIdElement.querySelectorAll('.timeline-item-month');
                let targetMonthElement = null;
                monthElements.forEach(monthElement => {
                    const monthText = monthElement.querySelector('.timeline-right').textContent; // 获取月份文本内容
                    if (monthText.includes(String(monthName) + '月')) { // 检查是否包含指定的月份
                        targetMonthElement = monthElement; // 如果包含，则将该月份元素赋值给targetMonthElement
                    }
                });
                // console.log(targetMonthElement);
                toggleCurrentMonthDisplay(targetMonthElement);
            }

            const totalPages = Math.ceil(ChatMsgIndex.length / itemsPerPage);
            // document.getElementById('curPage').innerHTML = currentPage;
            document.getElementById('gotoPage').value = currentPage;
            document.getElementById('maxPage').innerHTML = totalPages;
            const container = document.getElementById('chat-container');
            const OnePageMessage = document.createElement('div');

            // 计算当前页应该显示的元素范围
            const startIndex = (page - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;

            var newMapID = {};

            function checkImageExists(url, callback) {
                var img = new Image();
                img.onload = function () {
                    callback(true); // 图片存在
                };
                img.onerror = function () {
                    callback(false); // 图片不存在
                };
                img.src = url;
            }

            // 工具类函数
            function replaceEmoji(text) {
                if (!text) {
                    return '';
                }

                // 定义替换规则
                var emoji_set = new Set(['右哼哼', '礼物', '傲慢', '打脸', '流泪', '失望', '猪头', '福', '抱拳', '憨笑', '拳头', '抓狂',
                   '闭嘴', '白眼', '嘘', '害羞', '无语', '捂脸', '弱', '骷髅', '耶', '吐舌', '爱心', '吃瓜', '發', '囧',
                   '再见', '睡', '恐惧', '撇嘴', '尴尬', '烟花', '咖啡', '裂开', '勾引', '亲亲', '阴险', '偷笑', '得意',
                   '凋谢', '爆竹', '玫瑰', '翻白眼', 'Emm', '旺柴', '便便', '蛋糕', '天啊', '让我看看', '嘿哈', '难过',
                   '发呆', '難受', '庆祝', '好的', '发抖', '心碎', '抠鼻', '机智', '炸弹', '擦汗', '疑问', '脸红',
                   '呲牙', '委屈', '汗', '苦涩', '破涕为笑', '胜利', '菜刀', '惊恐', '太阳', 'OK', '悠闲', '合十',
                   '快哭了', '鼓掌', '拥抱', '加油加油', '转圈', 'Whimper', '坏笑', '笑脸', '生病', '吐', '调皮',
                   '叹气', '敲打', '可怜', '发怒', '阴脸', '晕', '衰', '跳跳', '困', '社会社会', '色', '红包', '鄙视',
                   '愉快', '加油', '皱眉', '啤酒', '咒骂', '哇', '惊讶', '握手', '大哭', '666', '奸笑', '月亮', '强',
                   '微笑', '嘴唇']);

                   var emoji_mapping = {
                    "愉快": "愉快",
                    "白眼": "白眼",
                    "傲慢": "傲慢",
                    "困": "困",
                    "惊恐": "惊恐",
                    "憨笑": "憨笑",
                    "悠闲": "悠闲",
                    "咒骂": "咒骂",
                    "疑问": "疑问",
                    "嘘": "嘘",
                    "晕": "晕",
                    "衰": "衰",
                    "骷髅": "骷髅",
                    "敲打": "敲打",
                    "Bye": "再见",
                    "擦汗": "擦汗",
                    "抠鼻": "抠鼻",
                    "鼓掌": "鼓掌",
                    "坏笑": "坏笑",
                    "右哼哼": "右哼哼",
                    "鄙视": "鄙视",
                    "委屈": "委屈",
                    "快哭了": "快哭了",
                    "阴险": "阴险",
                    "亲亲": "亲亲",
                    "可怜": "可怜",
                    "Happy": "笑脸",
                    "Sick": "生病",
                    "Flushed": "脸红",
                    "Lol": "破涕为笑",
                    "Terror": "恐惧",
                    "LetDown": "失望",
                    "Duh": "无语",
                    "Hey": "嘿哈",
                    "Facepalm": "捂脸",
                    "Smirk": "奸笑",
                    "Smart": "机智",
                    "Concerned": "皱眉",
                    "Yeah!": "耶",
                    "Onlooker": "吃瓜",
                    "GoForIt": "加油",
                    "Sweats": "汗",
                    "OMG": "天啊",
                    "Emm": "Emm",
                    "Respect": "社会社会",
                    "Doge": "旺柴",
                    "NoProb": "好的",
                    "MyBad": "打脸",
                    "Wow": "哇",
                    "Boring": "翻白眼",
                    "666": "666",
                    "LetMeSee": "让我看看",
                    "Sigh": "叹气",
                    "Hurt": "苦涩",
                    "Broken": "裂开",
                    "嘴唇": "嘴唇",
                    "爱心": "爱心",
                    "心碎": "心碎",
                    "拥抱": "拥抱",
                    "强": "强",
                    "弱": "弱",
                    "握手": "握手",
                    "胜利": "胜利",
                    "Salute": "抱拳",
                    "勾引": "勾引",
                    "拳头": "拳头",
                    "OK": "OK",
                    "Worship": "合十",
                    "啤酒": "啤酒",
                    "咖啡": "咖啡",
                    "蛋糕": "蛋糕",
                    "玫瑰": "玫瑰",
                    "凋谢": "凋谢",
                    "菜刀": "菜刀",
                    "炸弹": "炸弹",
                    "便便": "便便",
                    "月亮": "月亮",
                    "太阳": "太阳",
                    "Party": "庆祝",
                    "gift": "礼物",
                    "Packet": "红包",
                    "Rich": "發",
                    "Blessing": "福",
                    "Fireworks": "烟花",
                    "Firecracker": "爆竹",
                    "猪头": "猪头",
                    "跳跳": "跳跳",
                    "发抖": "发抖",
                    "转圈": "转圈",
                    "微笑": "微笑",
                    "撇嘴": "撇嘴",
                    "色": "色",
                    "发呆": "发呆",
                    "得意": "得意",
                    "流泪": "流泪",
                    "害羞": "害羞",
                    "闭嘴": "闭嘴",
                    "睡": "睡",
                    "大哭": "大哭",
                    "尴尬": "尴尬",
                    "发怒": "发怒",
                    "调皮": "调皮",
                    "呲牙": "呲牙",
                    "惊讶": "惊讶",
                    "难过": "难过",
                    "Blush": "脸红",
                    "抓狂": "抓狂",
                    "吐": "吐",
                    "偷笑": "偷笑"
                }

                // 使用正则表达式匹配形如 [xxx] 的字符串
                return text.replace(/\[([^\]]+)\]/g, function(match, p1) {
                    // 获取匹配的内容
                    var emojiName = p1.trim();
                    if (emojiName in emoji_mapping) {
                        emojiName = emoji_mapping[emojiName];
                    }
                    if (emoji_set.has(emojiName)){
                        // 如果在 emojiMap 中找到了对应的图片路径，则替换
                        return `<img src="./emoji/${emojiName}.png" id="${emojiName}" class="emoji_img">`;
                    }
                    // 如果没有找到对应的图片路径，则返回原始内容
                    return match;
                });
            }
            
            function timestampToTime(timestamp) {
                let date = new Date(timestamp * 1000);
                let year = date.getFullYear() + '-';
                let month = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                let day = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
                let hour = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                let minute = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                let second = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
                return year + month + day + hour + minute + second;
            }

            // 生成各类标签的函数
            function add5MinTimeTag(message) {
                if (message.timestamp - lastTimeStamp > 300) {
                    const newTimeMessage = document.createElement('div');
                    newTimeMessage.className = "item item-center";
                    newTimeMessage.innerHTML = `<span>${timestampToTime(message.timestamp)}</span>`;
                    // chatContainer.appendChild(newTimeMessage);
                    OnePageMessage.appendChild(newTimeMessage);
                    lastTimeStamp = message.timestamp;
                    // console.log("增加时间元素", timestampToTime(message.timestamp));
                }
            }

            function messageBubble(message, side) {
                const messageBubbleTag = document.createElement('div');
                messageBubbleTag.className = `bubble bubble-${side}`;
                messageBubbleTag.innerHTML = replaceEmoji(message.text);
                return messageBubbleTag;
            }

            function displayNameBox(message) {
                const displayName = document.createElement('div');
                displayName.className = "displayname";
                displayName.innerHTML = message.display_name;
                return displayName;
            }
            // function checkImageExists(url, callback) {
            //     var img = new Image();
            //     img.onload = function () {
            //         callback(true); // 图片存在
            //     };
            //     img.onerror = function () {
            //         callback(false); // 图片不存在
            //     };
            //     img.src = url;
            // }

            function avatarBox(message) {
                const avatarTag = document.createElement('div');
                avatarTag.className = "avatar";
                // var avatar_path = '';
                // // 使用示例
                // var imageUrl = avatarPaths[message.avatar_path];
                var imageUrl = message.avatar_src;
                // checkImageExists(imageUrl, function (exists) {
                //     if (exists) {
                //         console.log('图片存在');
                //     } else {
                //         imageUrl = avatarUrls[message.avatar_path]
                //     }
                //     avatarTag.innerHTML = `<img src="${imageUrl}" loading="lazy" />`
                // });
                avatarTag.innerHTML = `<img src="${imageUrl}" loading="lazy" />`
                return avatarTag;
            }


            // function messageImgBox(message) {
            //     const messageImgTag = document.createElement('div');
            //     if(message.type == 47) {
            //         messageImgTag.className = `emoji-image`;
            //     }else{
            //         messageImgTag.className = `chat-image`;
            //     }
            //     messageImgTag.innerHTML = `<img src="${message.path}" onclick="showModal(this)" loading="lazy"/>`;
            //     return messageImgTag;
            // }
            function messageImgBox(message) {
                const messageImgTag = document.createElement('div');
                
                if (message.type == 47) {
                    messageImgTag.className = 'emoji-image';
                    messageImgTag.innerHTML = `<img src="${message.path}" onclick="showModal(this)" loading="lazy"/>`;
                    return messageImgTag;
                } else {
                    messageImgTag.className = 'chat-image';
                }

                const basePath = message.path; // 图片的原始路径(没有后缀名)
                console.log('basePath:', basePath);
                const extensions = ['.jpg','.png', '.jpeg', '.gif', '.bmp']; // 常见的图片后缀
                let imgLoaded = false;

                // 尝试不同的后缀名
                extensions.forEach(extension => {
                    const img = new Image();
                    img.src = basePath + extension;

                    img.onload = function() {
                        messageImgTag.innerHTML = `<img src="${img.src}" onclick="showModal(this)" loading="lazy"/>`;
                        imgLoaded = true;
                    };

                    img.onerror = function() {
                        // 图片加载失败，继续尝试下一个后缀
                        if (!imgLoaded) {
                            // 这里可以选择在所有后缀都尝试完后进行处理
                        }
                    };
                });

                return messageImgTag;
            }


            function messageVideoBox(message) {
                const messageVideoTag = document.createElement('div');
                messageVideoTag.className = `chat-video`;
                messageVideoTag.innerHTML = `<video src="${message.path}" controls />`;
                return messageVideoTag;
            }


            function messageElementReferText(message, side) {
                const messageElementRefer = document.createElement('div');
                messageElementRefer.className = `chat-refer chat-refer-${side}`;
                messageElementRefer.innerHTML = replaceEmoji(message.quote_text);
                messageElementRefer.addEventListener('contextmenu', function () {
                    // 阻止默认的右键菜单
                    event.preventDefault();
                    // 创建右键菜单
                    var contextMenu = document.createElement('div');
                    contextMenu.className = 'custom-menu';
                    contextMenu.style.position = 'absolute';
                    contextMenu.style.left = event.pageX + 'px';
                    contextMenu.style.top = event.pageY + 'px';

                    // 添加菜单项
                    var menuItem = document.createElement('div');
                    menuItem.className = 'custom-menu-item';
                    menuItem.textContent = '定位到原文位置';
                    menuItem.addEventListener('click', function () {
                        contextMenu.remove();
                        referId = message.quote_server_id;
                        lastPage = currentPage;
                        currentPage = server_id_Page[String(referId)];

                        if (lastPage != currentPage) {
                            reachedBottom = false;
                            reachedTop = false;
                            renderPage(currentPage);
                        }
                        var targetSection = document.getElementById(referId);
                        targetSection.scrollIntoView({ behavior: 'smooth' });
                    });
                    contextMenu.appendChild(menuItem);

                    // 将右键菜单添加到页面上
                    document.body.appendChild(contextMenu);

                    // 点击页面其他地方时隐藏菜单
                    document.addEventListener('click', function (event) {
                        if (!contextMenu.contains(event.target)) {
                            contextMenu.remove();
                        }
                    });


                });
                return messageElementRefer;
            }

            function messageVoiceToTextBubble(message, side) {
                const messageVoiceToTextTag = document.createElement('div');
                messageVoiceToTextTag.className = `bubble bubble-${side}`;
                messageVoiceToTextTag.innerHTML = message.voice_to_text;
                return messageVoiceToTextTag;
            }

            function messageAudioBox(message) {
                const messageAudioTag = document.createElement('div');
                if (message.is_send) {
                    messageAudioTag.className = `bubble-audio-right`;
                    var messageAudioTagID = message.server_id + "_audio_box";
                    messageAudioTag.id = messageAudioTagID;
                    var audioID = message.server_id + "_audio";
                    AudioTag = document.createElement('audio');
                    AudioTag.id = audioID;
                    AudioTag.innerHTML = `<source src="${message.path}" type="audio/mpeg">`;
                    // 根据语音时长设置气泡宽度
                    var duration = Math.ceil(message.duration / 1000);
                    var bubblewidth = 40 + duration * 5;
                    if (bubblewidth < 250) {
                        messageAudioTag.style.width = `${bubblewidth}px`;
                    } else {
                        messageAudioTag.style.width = `250px`;
                    }
                    messageAudioTag.innerHTML = `''${duration}`;
                    messageAudioTag.appendChild(AudioTag);

                    $(function () {
                        messageAudioTag.addEventListener('click', function () {
                            event.stopPropagation();//防止冒泡
                            var AudioTag = messageAudioTag.querySelector('audio');
                            AudioTag.addEventListener('error', function () {
                                // 文件加载错误时执行的操作
                                alert('音频文件加载错误，文件不存在或无法访问。');
                            });
                            if (AudioTag !== null) {
                                if (AudioTag.paused) {
                                    AudioTag.play();
                                    if (AudioTag.networkState === HTMLMediaElement.NETWORK_NO_SOURCE) {
                                        alert('音频文件加载错误，文件不存在或无法访问!');
                                        return;
                                    }
                                    messageAudioTag.style.background = 'url(data:image/gif;base64,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) no-repeat center right 10px';
                                    messageAudioTag.style.backgroundSize = '20px';
                                    messageAudioTag.style.backgroundColor = '#9eea6a';
                                    var checkAudioInterval = setInterval(function () {
                                        if (AudioTag.paused) {
                                            messageAudioTag.style.background = 'url(data:image/png;base64,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) no-repeat center right 10px';
                                            messageAudioTag.style.backgroundSize = '20px';
                                            messageAudioTag.style.backgroundColor = '#9eea6a';
                                            clearInterval(checkAudioInterval);
                                        }
                                    }, 100);
                                }
                                else {
                                    messageAudioTag.style.background = 'url(data:image/png;base64,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) no-repeat center right 10px';
                                    messageAudioTag.style.backgroundSize = '20px';
                                    messageAudioTag.style.backgroundColor = '#9eea6a';
                                    AudioTag.pause();
                                }
                            }
                        });

                    });

                } else {
                    messageAudioTag.className = `bubble-audio-left`;
                    var messageAudioTagID = message.server_id + "_audio_box";
                    messageAudioTag.id = messageAudioTagID;
                    var audioID = message.server_id + "_audio";
                    AudioTag = document.createElement('audio');
                    AudioTag.id = audioID;
                    AudioTag.innerHTML = `<source src="${message.path}" type="audio/mpeg">`;
                    var duration = Math.ceil(message.duration / 1000);
                    var bubblewidth = 40 + duration * 5;
                    if (bubblewidth < 250) {
                        messageAudioTag.style.width = `${bubblewidth}px`;
                    } else {
                        messageAudioTag.style.width = `250px`;
                    }
                    messageAudioTag.innerHTML = `${duration}''`;
                    messageAudioTag.appendChild(AudioTag);

                    $(function () {
                        //播放器控制
                        messageAudioTag.addEventListener('click', function () {
                            event.stopPropagation();//防止冒泡
                            var AudioTag = messageAudioTag.querySelector('audio');
                            if (AudioTag !== null) {
                                if (AudioTag.paused) {
                                    AudioTag.play();
                                    if (AudioTag.networkState === HTMLMediaElement.NETWORK_NO_SOURCE) {
                                        alert('音频文件加载错误，文件不存在或无法访问!');
                                        return;
                                    }
                                    messageAudioTag.style.background = 'url(data:image/gif;base64,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) no-repeat center left 10px';
                                    messageAudioTag.style.backgroundSize = '20px';
                                    messageAudioTag.style.backgroundColor = '#fff';
                                    var checkAudioInterval = setInterval(function () {
                                        if (AudioTag.paused) {
                                            messageAudioTag.style.background = 'url(data:image/png;base64,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) no-repeat center left 10px';
                                            messageAudioTag.style.backgroundSize = '20px';
                                            messageAudioTag.style.backgroundColor = '#fff';
                                            clearInterval(checkAudioInterval);
                                        }
                                    }, 100);
                                }
                                else {
                                    messageAudioTag.style.background = 'url(data:image/png;base64,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) no-repeat center left 10px';
                                    messageAudioTag.style.backgroundSize = '20px';
                                    messageAudioTag.style.backgroundColor = '#fff';
                                    AudioTag.pause();
                                }
                            }
                        });
                    });
                }

                return messageAudioTag;
            }

            function messageCard(message) {
                const messageTag = document.createElement('div');
                messageTag.className = `card`;
                messageTag.innerHTML = `<a href="${message.url}" target="_blank">
                        <div class="card-content">
                            <div title="${message.title}" class="card-title">${message.title}</div>
                            ${message.description ? `
                                <div class="description">
                                <p title="${message.description}">${message.description}</p>
                                <img loading="lazy" class="thumbnail" src="${message.cover_url}" alt="Thumbnail" onerror="this.style.display='none';">
                                </div>` : ''}
                        </div>
                        <div class="link-info">
                            ${message.app_logo ? `<img loading="lazy" class="app-logo" src="${message.app_logo}" alt="App Logo" onerror="this.style.display='none';">` : ''}
                            ${message.app_name ? `<span class="app-name">${message.app_name}</span>` : `<span class="app-name">app-name</span>`}
                        </div>
                    </a>`;

                return messageTag;
            }

            function getFileSize(byteSize, format = 'MB') {
                // 定义转换因子
                const units = {
                    'B': 1,
                    'KB': 1024,
                    'MB': 1024 ** 2,
                    'GB': 1024 ** 3,
                };

                // 检查格式是否支持
                if (units.hasOwnProperty(format)) {
                    const sizeInFormat = byteSize / units[format];
                    return `${sizeInFormat.toFixed(2)} ${format}`;
                } else {
                    throw new Error(`Unsupported format: ${format}`);
                }
            }
            function getFileExtension(file_type) {
                icon_files = {
                    'doc': 'DOCX',
                    'docx': 'DOCX',
                    'xls': 'XLSX',
                    'xlsx': 'XLSX',
                    'csv': 'CSV',
                    'txt': 'TXT',
                    'zip': 'ZIP',
                    '7z': 'ZIP',
                    'rar': 'ZIP',
                    'ppt':'PPT',
                    'pptx':'PPT',
                    'pdf': 'PDF'
                }
                return icon_files.hasOwnProperty(file_type) ? icon_files[file_type] : 'Default';
                // 如果没有匹配到后缀名，返回 'Default'
                return 'Default';
            }
            function messageFileBox(message) {
                const messageFileTag = document.createElement('div');
                messageFileTag.className = `chat-file`;
                messageFileTag.onclick = function (event) {
                    if (message.path !== '') {
                        window.open(message.path, '_blank');
                    } else {
                        alert("文件可能丢失、过期或不存放在该主机上")
                    }
                }
                

                messageFileTag.innerHTML = `<div class="file-box">
                    <div class="file-info flex1">
                        <div class="file-title" title="${message.file_name}">${message.file_name}</div>
                        <div class="file-size">${getFileSize(message.file_size)}</div>
                    </div>
                    <div class="file-img flex2">
                            <img src="${FileIcons[getFileExtension(message.file_type)]}"/>
                    </div>
                </div>` +
                    (message.app_name ? `<div class="app-info"><p>${message.app_name}</p></div>` : "");
                return messageFileTag;
            }
            function messageVideoAudioCall(message, side) {
                const message_call_box = document.createElement('div');
                message_call_box.className = `bubble bubble-${side} call`;
                message_call_box.setAttribute("calltype", message.invite_type)
                message_call_box.innerHTML = `<i></i><span>${message.display_content}<span>`;
                return message_call_box;
            }
            function messageMusicAudioBox(message) {
                const messageMusicAudioTag = document.createElement('div');
                messageMusicAudioTag.className = `chat-music-audio`;
                messageMusicAudioTag.dataset.link = message.url;
                messageMusicAudioTag.onclick = function (event) {
                    if (!event.target.classList.contains('play-button')) {
                        window.open(message.url, '_blank');
                    }
                }
                if (message.title.length >= 12) {
                    message.title = message.title.slice(0, 12) + '...'
                }
                if (message.description.length >= 10) {
                    message.description = message.description.slice(0, 10) + '...'
                }
                messageMusicAudioTag.innerHTML = `<div class="player-box">
                    <div>
                        <img src="${message.cover_url}" alt="Cover Image" class="cover-image">
                    </div>
                    <div class="player-info">
                        <div class="title">${message.title}</div>
                        <div class="artist">${message.description}</div>
                    </div>
                    <div class="player-controls">
                    </div>
                </div>
                <div class="player-original">
                    <svg class="player-original-img" t="1727442389694" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4302" width="64" height="64"><path d="M35.84 529.92c0 263.68 215.04 478.72 478.72 478.72 263.68 0 478.72-215.04 478.72-478.72C993.28 266.24 780.8 51.2 514.56 51.2 250.88 51.2 35.84 266.24 35.84 529.92z" fill="#F8C913" p-id="4303"></path><path d="M660.48 10.24c-17.92 20.48-56.32 38.4-107.52 51.2-87.04 20.48-104.96 25.6-130.56 40.96-15.36 7.68-33.28 20.48-43.52 30.72-20.48 17.92-35.84 51.2-30.72 61.44 2.56 5.12 51.2 74.24 110.08 158.72 58.88 81.92 115.2 163.84 128 181.76 12.8 17.92 20.48 33.28 20.48 33.28 0 2.56-10.24 0-20.48-2.56-40.96-12.8-112.64 0-163.84 25.6-38.4 20.48-81.92 64-99.84 99.84-20.48 40.96-23.04 97.28-7.68 135.68 15.36 40.96 48.64 74.24 92.16 97.28 33.28 17.92 40.96 17.92 84.48 20.48 35.84 2.56 56.32 0 76.8-5.12 94.72-28.16 163.84-102.4 168.96-189.44 2.56-48.64-2.56-64-66.56-176.64-99.84-174.08-189.44-332.8-189.44-335.36 0 0 17.92-2.56 40.96-5.12 66.56-2.56 120.32-35.84 148.48-89.6 12.8-23.04 12.8-30.72 12.8-79.36 0-30.72-2.56-58.88-5.12-61.44-2.56-7.68-5.12-5.12-17.92 7.68z" fill="#02B053" p-id="4304"></path></svg>
                    <p>${message.app_name}</p>
                </div>
                `
                if (message.text != '') {
                    var audio = document.createElement('audio');
                    audio.src = message.text;
                    messageMusicAudioTag.querySelector('.player-controls').append(audio)
                }
                ;
                var playButton = document.createElement('button');
                playButton.className = 'play-button paused';
                playButton.onclick = function (event) {
                    event.stopPropagation(); // 阻止点击播放按钮时触发父级的点击事件
                    toggleAudio(event.target);
                };
                if (message.is_send) {
                    messageMusicAudioTag.querySelector('.player-controls').append(playButton)
                } else {
                    messageMusicAudioTag.querySelector('.player-controls').prepend(playButton)
                }
                return messageMusicAudioTag;
            }
            function messageTransfer(message) {
                const transfer_box = document.createElement("div");
                transfer_box.className = "transfer-box";
                transfer_box.setAttribute("issend", message.is_send);
                transfer_box.setAttribute("paysubtype", message.pay_subtype);
                transfer_box.innerHTML = `<div class="transfer">
                <div class="trans-content"><i></i>
                    <div class="transfer-texts"><span>${message.fee_desc}</span><font>${message.text}</font></div>
                </div>
                <div class="trans-bottom"><span>聊天转账</span></div>
            </div>`;
                return transfer_box;
            }
            function personalCard(message) {
                const personal_card = document.createElement("div");
                personal_card.className = "personal-card";
                const contner = document.createElement("div");
                contner.className = "contner";
                contner.innerHTML = `<img src="${message.small_head_url}" alt="headimg">`
                const contner_text = document.createElement("div");
                contner_text.className = "text";
                if (message.sex == '男') {
                    contner_text.innerHTML = `<div class="nickname">
                    ${message.nickname}<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1704295160809" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4268" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><path d="M922.344176 152.209186v-0.050575c0-0.765851-0.0867-1.517251-0.130051-2.283101-0.036125-0.67915-0.04335-1.365526-0.108375-2.037452-0.0867-0.794751-0.2312-1.553376-0.36125-2.333676-0.07225-0.455175-0.122825-0.917576-0.209525-1.379976l-0.04335-0.296226-0.050575-0.281775c-0.15895-0.794751-0.397375-1.553376-0.599676-2.333677-0.15895-0.6069-0.289-1.228251-0.469625-1.820701-0.2601-0.816426-0.585225-1.618401-0.888676-2.420377l-0.27455-0.758625-0.166175-0.455176-0.13005-0.325125c-0.354025-0.830876-0.758626-1.625626-1.163226-2.427602-0.223975-0.455175-0.41905-0.917576-0.6647-1.358301-0.4335-0.801976-0.924801-1.553376-1.401651-2.319226-0.1445-0.223975-0.267325-0.4624-0.411826-0.671926-0.0867-0.13005-0.151725-0.267325-0.238425-0.397375l-0.1156-0.1734c-0.50575-0.751401-1.062076-1.445001-1.611176-2.153052-0.30345-0.39015-0.578-0.801976-0.888676-1.184901-0.556325-0.6647-1.163226-1.293276-1.755676-1.929076-0.354025-0.382925-0.67915-0.780301-1.040401-1.141551l-0.065025-0.065025-0.01445-0.021675-0.07225-0.0578c-0.527425-0.527425-1.098201-1.004276-1.668976-1.510026-0.426275-0.3757-0.823651-0.780301-1.271601-1.148776-0.599675-0.4913-1.249926-0.932026-1.878501-1.401651-0.484075-0.36125-0.953701-0.744176-1.452226-1.083751a5.086404 5.086404 0 0 1-0.332351-0.2023l-0.53465-0.310675a26.70362 26.70362 0 0 0-1.141551-0.693601c-0.5202-0.3179-1.025951-0.657475-1.567826-0.9537-0.67915-0.368475-1.394426-0.671925-2.095252-1.004276-0.56355-0.267325-1.112651-0.570775-1.690651-0.809201-0.151725-0.07225-0.325125-0.122825-0.4913-0.180625-0.18785-0.079475-0.39015-0.137275-0.578001-0.223975l-1.018725-0.368475c-0.628575-0.223975-1.242701-0.484075-1.892952-0.693601-0.643025-0.18785-1.307726-0.325125-1.965201-0.4913-0.729726-0.195075-1.445001-0.41905-2.189177-0.563551-0.151725-0.036125-0.310675-0.036125-0.4624-0.065025l-0.426275-0.07225c-0.527425-0.10115-1.069301-0.166175-1.618402-0.238425-0.599675-0.093925-1.184901-0.21675-1.784576-0.281775a42.772031 42.772031 0 0 0-4.428928-0.223975h-0.151725l-343.079377 1.235476a43.350032 43.350032 0 0 0 0.151726 86.700063h0.151725l238.417949-0.867-156.76094 157.895265c-53.710689-42.179581-121.170564-67.467099-194.150342-67.467099l-4.219403 0.021675c-174.093728 2.290327-313.85423 145.786157-311.563903 319.858209 2.261427 171.817851 143.849855 311.592803 315.631581 311.592803l4.219403-0.0289c84.330262-1.105426 163.18397-34.983476 222.031638-95.39897 58.854893-60.415494 90.644916-140.136203 89.532265-224.452014-0.881451-67.82835-23.553517-130.613646-61.159669-181.831708l157.61349-158.747816V495.418613a43.350032 43.350032 0 0 0 86.700064 0V152.346462v-0.050575-0.086701zM577.198448 751.978551c-39.990404 41.06693-93.585494 64.085797-150.901461 64.844422l-2.890002 0.01445c-116.73441 0-212.957031-95.001595-214.495957-211.77213C207.357652 486.748607 302.352021 389.23271 420.668708 387.672109l2.890002-0.007225c116.741636 0 212.971481 94.99437 214.488732 211.779355 0.758626 57.301517-20.851365 111.474607-60.848994 152.534312z" fill="#1296db" p-id="4269"></path></svg></div>`
                } else if (message.sex == '女') {
                    contner_text.innerHTML = `<div class="nickname">
                    ${message.nickname}<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1704295202245" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5558" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><path d="M870.69952 153.30048c-66.10688-66.10944-154.00448-102.51776-247.49696-102.51904-93.49248 0-181.3888 36.40832-247.49696 102.51648-66.11072 66.10944-102.51648 154.00576-102.51648 247.49952-0.00128 76.19968 24.19072 148.6784 68.93056 208.67584l-60.2048 60.20352L138.18496 525.94688c-19.99488-19.99488-52.41216-19.99488-72.40704 0-19.99488 19.9936-19.99488 52.41216 0 72.40704l143.72992 143.72992-55.75552 55.75552c-19.9936 19.9936-19.9936 52.41216 0 72.40576 19.99616 19.99616 52.41344 19.99488 72.40832 0l55.75424-55.75424 143.7312 143.72992c19.9936 19.99488 52.41088 19.99488 72.40576 0 19.99616-19.99488 19.99616-52.41216 0-72.40704L354.32192 742.08512l60.2048-60.20352c59.99872 44.73856 132.47616 68.93184 208.67584 68.93184 93.49248 0 181.38752-36.40832 247.49696-102.51776s102.51776-154.00576 102.51776-247.49824C973.21728 307.30496 936.80768 219.40736 870.69952 153.30048zM798.29248 575.88736c-46.76736 46.76736-108.94976 72.5248-175.08992 72.5248s-128.32128-25.75744-175.08992-72.5248c-46.76864-46.76736-72.5248-108.94976-72.5248-175.08992 0.00128-66.14272 25.75616-128.32384 72.5248-175.0912 46.76736-46.76864 108.94976-72.5248 175.08992-72.5248 66.14016 0.00128 128.32256 25.75744 175.08992 72.52608 46.76736 46.76736 72.5248 108.94848 72.5248 175.08864C870.81728 466.9376 845.0624 529.11872 798.29248 575.88736z" fill="#d81e06" p-id="5559"></path></svg></div>`
                } else {
                    contner_text.innerHTML = `<div class="nickname">
                    ${message.nickname}</div>`
                }
                contner_text.innerHTML += `<div class="other">微信号: ${message.alias}</div>
                                <div class="other">地区: ${message.province}${message.city}</div>`
                contner.appendChild(contner_text);
                personal_card.appendChild(contner);
                const card_bottom = document.createElement("div");
                card_bottom.className = "bottom";
                if (message.username.includes("gh_")) {
                    card_bottom.innerHTML = `<div class="text">公众号名片</div>`
                } else {
                    card_bottom.innerHTML = `<div class="text">个人名片</div>`
                }
                personal_card.appendChild(card_bottom);
                return personal_card;
            }
            function OpenIMCard(message) {
                const OpenIM_card = document.createElement("div");
                OpenIM_card.className = "OpenIM-card";
                const contner = document.createElement("div");
                contner.className = "contner";
                contner.innerHTML = `<img src="${message.small_head_url}" alt="headimg">`
                const contner_text = document.createElement("div");
                contner_text.className = "text";
                if (message.sex == '男') {
                    contner_text.innerHTML = `<div class="nickname">
                    ${message.nickname}<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1704295160809" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4268" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><path d="M922.344176 152.209186v-0.050575c0-0.765851-0.0867-1.517251-0.130051-2.283101-0.036125-0.67915-0.04335-1.365526-0.108375-2.037452-0.0867-0.794751-0.2312-1.553376-0.36125-2.333676-0.07225-0.455175-0.122825-0.917576-0.209525-1.379976l-0.04335-0.296226-0.050575-0.281775c-0.15895-0.794751-0.397375-1.553376-0.599676-2.333677-0.15895-0.6069-0.289-1.228251-0.469625-1.820701-0.2601-0.816426-0.585225-1.618401-0.888676-2.420377l-0.27455-0.758625-0.166175-0.455176-0.13005-0.325125c-0.354025-0.830876-0.758626-1.625626-1.163226-2.427602-0.223975-0.455175-0.41905-0.917576-0.6647-1.358301-0.4335-0.801976-0.924801-1.553376-1.401651-2.319226-0.1445-0.223975-0.267325-0.4624-0.411826-0.671926-0.0867-0.13005-0.151725-0.267325-0.238425-0.397375l-0.1156-0.1734c-0.50575-0.751401-1.062076-1.445001-1.611176-2.153052-0.30345-0.39015-0.578-0.801976-0.888676-1.184901-0.556325-0.6647-1.163226-1.293276-1.755676-1.929076-0.354025-0.382925-0.67915-0.780301-1.040401-1.141551l-0.065025-0.065025-0.01445-0.021675-0.07225-0.0578c-0.527425-0.527425-1.098201-1.004276-1.668976-1.510026-0.426275-0.3757-0.823651-0.780301-1.271601-1.148776-0.599675-0.4913-1.249926-0.932026-1.878501-1.401651-0.484075-0.36125-0.953701-0.744176-1.452226-1.083751a5.086404 5.086404 0 0 1-0.332351-0.2023l-0.53465-0.310675a26.70362 26.70362 0 0 0-1.141551-0.693601c-0.5202-0.3179-1.025951-0.657475-1.567826-0.9537-0.67915-0.368475-1.394426-0.671925-2.095252-1.004276-0.56355-0.267325-1.112651-0.570775-1.690651-0.809201-0.151725-0.07225-0.325125-0.122825-0.4913-0.180625-0.18785-0.079475-0.39015-0.137275-0.578001-0.223975l-1.018725-0.368475c-0.628575-0.223975-1.242701-0.484075-1.892952-0.693601-0.643025-0.18785-1.307726-0.325125-1.965201-0.4913-0.729726-0.195075-1.445001-0.41905-2.189177-0.563551-0.151725-0.036125-0.310675-0.036125-0.4624-0.065025l-0.426275-0.07225c-0.527425-0.10115-1.069301-0.166175-1.618402-0.238425-0.599675-0.093925-1.184901-0.21675-1.784576-0.281775a42.772031 42.772031 0 0 0-4.428928-0.223975h-0.151725l-343.079377 1.235476a43.350032 43.350032 0 0 0 0.151726 86.700063h0.151725l238.417949-0.867-156.76094 157.895265c-53.710689-42.179581-121.170564-67.467099-194.150342-67.467099l-4.219403 0.021675c-174.093728 2.290327-313.85423 145.786157-311.563903 319.858209 2.261427 171.817851 143.849855 311.592803 315.631581 311.592803l4.219403-0.0289c84.330262-1.105426 163.18397-34.983476 222.031638-95.39897 58.854893-60.415494 90.644916-140.136203 89.532265-224.452014-0.881451-67.82835-23.553517-130.613646-61.159669-181.831708l157.61349-158.747816V495.418613a43.350032 43.350032 0 0 0 86.700064 0V152.346462v-0.050575-0.086701zM577.198448 751.978551c-39.990404 41.06693-93.585494 64.085797-150.901461 64.844422l-2.890002 0.01445c-116.73441 0-212.957031-95.001595-214.495957-211.77213C207.357652 486.748607 302.352021 389.23271 420.668708 387.672109l2.890002-0.007225c116.741636 0 212.971481 94.99437 214.488732 211.779355 0.758626 57.301517-20.851365 111.474607-60.848994 152.534312z" fill="#1296db" p-id="4269"></path></svg></div>`
                } else if (message.sex == '女') {
                    contner_text.innerHTML = `<div class="nickname">
                    ${message.nickname}<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1704295202245" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5558" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><path d="M870.69952 153.30048c-66.10688-66.10944-154.00448-102.51776-247.49696-102.51904-93.49248 0-181.3888 36.40832-247.49696 102.51648-66.11072 66.10944-102.51648 154.00576-102.51648 247.49952-0.00128 76.19968 24.19072 148.6784 68.93056 208.67584l-60.2048 60.20352L138.18496 525.94688c-19.99488-19.99488-52.41216-19.99488-72.40704 0-19.99488 19.9936-19.99488 52.41216 0 72.40704l143.72992 143.72992-55.75552 55.75552c-19.9936 19.9936-19.9936 52.41216 0 72.40576 19.99616 19.99616 52.41344 19.99488 72.40832 0l55.75424-55.75424 143.7312 143.72992c19.9936 19.99488 52.41088 19.99488 72.40576 0 19.99616-19.99488 19.99616-52.41216 0-72.40704L354.32192 742.08512l60.2048-60.20352c59.99872 44.73856 132.47616 68.93184 208.67584 68.93184 93.49248 0 181.38752-36.40832 247.49696-102.51776s102.51776-154.00576 102.51776-247.49824C973.21728 307.30496 936.80768 219.40736 870.69952 153.30048zM798.29248 575.88736c-46.76736 46.76736-108.94976 72.5248-175.08992 72.5248s-128.32128-25.75744-175.08992-72.5248c-46.76864-46.76736-72.5248-108.94976-72.5248-175.08992 0.00128-66.14272 25.75616-128.32384 72.5248-175.0912 46.76736-46.76864 108.94976-72.5248 175.08992-72.5248 66.14016 0.00128 128.32256 25.75744 175.08992 72.52608 46.76736 46.76736 72.5248 108.94848 72.5248 175.08864C870.81728 466.9376 845.0624 529.11872 798.29248 575.88736z" fill="#d81e06" p-id="5559"></path></svg></div>`
                } else {
                    contner_text.innerHTML = `<div class="nickname">
                    ${message.nickname}</div>`
                }
                contner_text.innerHTML += `<div class="desc"><img src="${message.open_im_desc_icon}">${message.open_im_desc_icon}</div>`
                contner.appendChild(contner_text);
                OpenIM_card.appendChild(contner);
                const card_bottom = document.createElement("div");
                card_bottom.className = "bottom";
                card_bottom.innerHTML = `<div class="text">企业微信名片</div>`
                OpenIM_card.appendChild(card_bottom);
                return OpenIM_card;
            }

            function messageLocation(message, newMapID) {
                const Location = document.createElement("div");
                Location.className = "location";
                Location.innerHTML = `<div class="poiname">${message.poiname}</div>
                <div class="label">${message.label}</div>
                `
                const map_box = document.createElement("div");
                map_box.className = "map";
                map_box.id = "map" + message.timestamp;
                Location.appendChild(map_box);

                newMapID["map" + message.timestamp] = [message.x, message.y, message.label, message.scale]

                return Location;
            }

            function loadMap(MapID) {
                // 点标记显示内容，HTML要素字符串
                var markerContent = '' +
                    '<div class="custom-content-marker">' +
                    '   <img src="https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png">' +
                    '</div>';
                // 遍历MapID
                for (var key in MapID) {
                    var map = new AMap.Map(key, {
                        resizeEnable: true,
                        center: [MapID[key][1], MapID[key][0]],
                        zoom: MapID[key][3]
                    });
                    const position = new AMap.LngLat(MapID[key][1], MapID[key][0]); //Marker 经纬度

                    const marker = new AMap.Marker({
                        position: position,
                        // content: 'label', //将 html 传给 content
                        // icon: "https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png",
                        content: markerContent,
                        title: MapID[key][2],
                        offset: new AMap.Pixel(-13, -30), //以 icon 的 [center bottom] 为原点
                    });
                    map.add(marker);
                }

            }

            function messageMergeCard(message, zindex=1, index=0) {
                const messageMergeCardTag = document.createElement('div');
                messageMergeCardTag.className = `merge-message`;
                console.log("mergeMessage", message);
                if (message.description && message.description !== null) {
                    const splitResults = message.description.split("\n");
                    const firstTwoItems = splitResults.slice(0, 2);

                    messageMergeCardTag.innerHTML = `<div class="title">${message.title}</div>
                                    <div class="msg">${firstTwoItems[0]}</div>
                                    <div class="msg">${firstTwoItems[1]}</div>`;
                } else {
                    messageMergeCardTag.innerHTML = `<div class="title">${message.title}</div>
                                <div class="msg"></div>
                                <div class="msg"></div>`;
                }
                const bottomTag = document.createElement('div');
                bottomTag.className = `bottom`;
                bottomTag.innerHTML = `<div class="text">聊天记录</div>`;
                messageMergeCardTag.appendChild(bottomTag);

                messageMergeCardTag.addEventListener('click', function () {
                    modalID = `${message.server_id}-${zindex}-${index}Modal`;
                    var modal = document.getElementById(modalID);
                    modal.style.display = "block";

                    var span = modal.querySelector('.close');

                    span.onclick = function () {
                        modal.style.display = "none";
                    }
                });
                return messageMergeCardTag;
            }

            function innermessageMergeCard(message, zindex=1, index=0, parent_server_id=0) {
                const innerMsgMerge_card = document.createElement("div")
                innerMsgMerge_card.className = "inner-msgMerge-card"
                if (message.description && message.description !== null) {
                    const splitResults = message.description.split("\n");
                    const firstTwoItems = splitResults.slice(0, 2);
                    innerMsgMerge_card.innerHTML = `<div class="title">${message.title}</div>
                                    <div class="desc">${firstTwoItems[0]}</div>
                                    <div class="desc">${firstTwoItems[1]}</div>`
                } else {
                    innerMsgMerge_card.innerHTML = `<div class="title">${message.title}</div>
                                <div class="desc"></div>
                                <div class="desc"></div>`
                }

                innerMsgMerge_card.addEventListener('click', function () {
                    modalID = `${parent_server_id}-${zindex}-${index}Modal`;
                    var modal = document.getElementById(modalID);
                    modal.style.display = "block";

                    var span = modal.querySelector('.close');

                    span.onclick = function () {
                        modal.style.display = "none";
                    }
                });
                return innerMsgMerge_card
            }

            function messageMergeModaltitle(message) {
                const messageMergeModaltitle = document.createElement('div');
                messageMergeModaltitle.className = `title`;
                if (message.title) {
                    messageMergeModaltitle.innerHTML = `<span class="close">&times;</span>
                    <div class="title">${message.title}</div>`;
                } else {
                    messageMergeModaltitle.innerHTML = `<span class="close">&times;</span>
                    <div class="title">${message.description}</div>`;
                }
                return messageMergeModaltitle;
            }

            function messageMiniProgram(message) {
                const message_MiniProgram = document.createElement('div');
                message_MiniProgram.className = `mini-program`;

                message_MiniProgram.innerHTML = `<a href="${message.url}" target="_blank">
                        <div class="top">
                            <img src='${message.app_logo}' alt='logo'>
                            <div class='text'>${message.app_name}</div>
                        </div>
                        <div class="title">${message.title}</div>
                        <div class="cover"><img src='${message.cover_url}'  onerror="this.style.display='none';"></div>
                        <div class="bottom"><div class="text">小程序</div></div>
                    </a>`;
                return message_MiniProgram;
            }

            function messageVideoNumber(message) {
                const message_VideoNumber = document.createElement('div');
                message_VideoNumber.className = `video-number`;
                const message_VideoNumber_title = document.createElement('div');
                message_VideoNumber_title.className = `title`;
                const message_VideoNumber_container = document.createElement('div');
                message_VideoNumber_container.className = `container`;
                message_VideoNumber_container.style.backgroundImage = `url(${message.cover_url})`;
                message_VideoNumber_container.style.backgroundSize = "cover";
                message_VideoNumber_container.style.backgroundRepeat = "no-repeat";
                message_VideoNumber_title.innerHTML = replaceEmoji(message.title);
                message_VideoNumber_container.innerHTML = `
                <div class="bottom">
                    <div class="author">
                        <div class="logo"><img src='${message.publisher_avatar}'  onerror="this.style.display='none';"></div>
                        <div class="name">${message.publisher_nickname}</div>
                        <div class="authIcon"><img src='${message.authIconUrl}' onerror="this.style.display='none';"></div>
                    </div>
                </div>`;

                message_VideoNumber.appendChild(message_VideoNumber_title);
                message_VideoNumber.appendChild(message_VideoNumber_container);

                return message_VideoNumber;
            }

            function handleTextMessage(params) {
                const { message, side, messageContent, messageElement, avatarTag, record, recordItem } = params;

                if (record) {
                    recordItem.innerHTML += `<div class="msg-container">${record.text}</div>`;
                } else {
                    messageContent.className = `content-wrapper content-wrapper-${side}`;
                    messageContent.appendChild(messageBubble(message, side));
                    messageElement.className = `item item-${side}`;
                    messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                    messageElement.appendChild(message.is_send ? avatarTag : messageContent);
                }

            }
            function handleSystemMessage(params) {
                const { message, messageElement } = params;
                messageElement.className = "item item-center";
                messageElement.innerHTML = `<span class="system-msg">${replaceEmoji(message.text.replace('\\"', '"'))}</span>`;
            }
            function handelAudioMessage(params) {
                const { message, side, messageContent, messageElement, avatarTag, record, recordItem } = params;

                if (record) {
                    recordItem.innerHTML += `<div class="msg-container">${record.text}</div>`;
                } else {
                    messageContent.className = `content-wrapper content-wrapper-${side}`;
                    messageContent.appendChild(messageAudioBox(message));
                    if (message.voice_to_text) {
                        messageContent.appendChild(messageVoiceToTextBubble(message, side));
                    }
                    messageElement.className = `item item-${side}`;
                    messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                    messageElement.appendChild(message.is_send ? avatarTag : messageContent);
                }
            }
            function handleImageOrEmojiMessage(params) {
                const { message, side, messageContent, messageElement, avatarTag, record, recordItem } = params;

                if (record) {
                    recordItem.appendChild(messageImgBox(record));
                } else {
                    messageContent.className = `content-wrapper content-wrapper-${side}`;
                    messageContent.appendChild(messageImgBox(message));
                    messageElement.className = `item item-${side}`;
                    messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                    messageElement.appendChild(message.is_send ? avatarTag : messageContent);
                }
            }

            function handleVideoMessage(params) {
                const { message, side, messageContent, messageElement, avatarTag, record, recordItem } = params;

                if (record) {
                    recordItem.appendChild(messageVideoBox(record));
                } else {
                    messageContent.className = `content-wrapper content-wrapper-${side}`;
                    messageContent.appendChild(messageVideoBox(message));
                    messageElement.className = `item item-${side}`;
                    messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                    messageElement.appendChild(message.is_send ? avatarTag : messageContent);
                }
            }

            function handleFileMessage(params) {
                const { message, side, messageContent, messageElement, avatarTag, record, recordItem } = params;
                if (record) {
                    record.icon_path = getFileIcon(record.datafmt)
                    recordItem.appendChild(messageFileBox(record));
                }
                else {
                    messageContent.className = `content-wrapper content-wrapper-${side}`;
                    messageContent.appendChild(messageFileBox(message));
                    messageElement.className = `item item-${side}`;
                    messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                    messageElement.appendChild(message.is_send ? avatarTag : messageContent);
                }
            }

            function handleTransferMessage(params) {
                const { message, side, messageContent, messageElement, avatarTag } = params;
                messageContent.className = `content-wrapper content-wrapper-${side}`;
                messageContent.appendChild(messageTransfer(message));
                // 整合
                messageElement.className = `item item-${side}`;
                messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                messageElement.appendChild(message.is_send ? avatarTag : messageContent);
            }

            function handleLinkMessage(params) {
                const { message, side, messageContent, messageElement, avatarTag, record, recordItem } = params;
                if (record) {
                    recordItem.appendChild(messageCard(record));
                }
                else {
                    messageContent.className = `content-wrapper content-wrapper-${side}`;
                    messageContent.appendChild(messageCard(message));
                    // 整合
                    messageElement.className = `item item-${side}`;
                    messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                    messageElement.appendChild(message.is_send ? avatarTag : messageContent);
                }
            }

            function handleMusicMessage(params) {
                const { message, side, messageContent, messageElement, avatarTag } = params;
                messageContent.className = `content-wrapper content-wrapper-${side}`;
                messageContent.appendChild(messageMusicAudioBox(message));
                // 整合
                messageElement.className = `item item-${side}`;
                messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                messageElement.appendChild(message.is_send ? avatarTag : messageContent);
            }

            function handleAppletMessage(params) {
                const { message, side, messageContent, messageElement, avatarTag } = params;
                messageContent.className = `content-wrapper content-wrapper-${side}`;
                messageContent.appendChild(messageMiniProgram(message));
                messageElement.className = `item item-${side}`;
                messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                messageElement.appendChild(message.is_send ? avatarTag : messageContent);
            }

            function handleMergedMessages(params) {
                const { message, side, messageContent, messageElement, avatarTag, mergemessageElement, record, recordItem, zindex, i, parent_server_id} = params;
                if (record) {
                    recordItem.appendChild(innermessageMergeCard(record, zindex + 1, i, parent_server_id));
                    messageContent.insertBefore(messageMergeModal(record, messageContent, zindex + 1, i, parent_server_id), messageContent.firstChild);
                }
                else {
                    messageContent.className = `content-wrapper content-wrapper-${side}`;

                    messageContent.appendChild(messageMergeCard(message));
                    messageContent.insertBefore(messageMergeModal(message, messageContent, 1), messageContent.firstChild);
                    messageElement.className = `item item-${side}`;
                    messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                    messageElement.appendChild(message.is_send ? avatarTag : messageContent);
                }
            }

            function handleQuoteMessage(params) {
                const { message, side, messageContent, messageElement, avatarTag } = params;
                messageContent.className = `content-wrapper content-wrapper-${side}`;
                messageContent.appendChild(messageBubble(message));
                if (message.quote_text) {
                    messageContent.appendChild(messageElementReferText(message, side));
                }
                messageElement.className = `item item-${side}`;
                messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                messageElement.appendChild(message.is_send ? avatarTag : messageContent);
            }

            function handleWeChatVideo(params) {
                const { message, side, messageContent, messageElement, avatarTag } = params;
                messageContent.className = `content-wrapper content-wrapper-${side}`;
                messageContent.appendChild(messageVideoNumber(message));
                messageElement.className = `item item-${side}`;
                messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                messageElement.appendChild(message.is_send ? avatarTag : messageContent);
            }

            function handleVideoAudioCall(params) {
                const { message, side, messageContent, messageElement, avatarTag } = params;
                messageContent.className = `content-wrapper content-wrapper-${side}`;
                messageContent.appendChild(messageVideoAudioCall(message));
                messageElement.className = `item item-${side}`;
                messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                messageElement.appendChild(message.is_send ? avatarTag : messageContent);
            }

            function handlePersonalCard(params) {
                const { message, side, messageContent, messageElement, avatarTag } = params;
                messageContent.className = `content-wrapper content-wrapper-${side}`;
                if (message.is_open_im) {
                    messageContent.appendChild(OpenIMCard(message));
                }
                else {
                    messageContent.appendChild(personalCard(message));
                }
                messageElement.className = `item item-${side}`;
                messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                messageElement.appendChild(message.is_send ? avatarTag : messageContent);
            }

            function handleLocationMessage(params) {
                const { message, side, messageContent, messageElement, avatarTag, record, recordItem } = params;
                if (record) {
                    recordItem.appendChild(messageLocation(record, MapID));
                }
                else {
                    messageContent.className = `content-wrapper content-wrapper-${side}`;
                    messageContent.appendChild(messageLocation(message, MapID));
                    messageElement.className = `item item-${side}`;
                    messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                    messageElement.appendChild(message.is_send ? avatarTag : messageContent);
                }
            }

            function handleOpenIMCard(params) {
                const { message, side, messageContent, messageElement, avatarTag } = params;
                messageContent.className = `content-wrapper content-wrapper-${side}`;
                messageContent.appendChild(OpenIMCard(message));
                messageElement.className = `item item-${side}`;
                messageElement.appendChild(message.is_send ? messageContent : avatarTag);
                messageElement.appendChild(message.is_send ? avatarTag : messageContent);
            }

            function handleUnkownMessage(params) {
                const { message } = params;
                console.log("UnkownMessage: ", message);
            }

            const messageHandlers = {
                [MessageType.Text]: handleTextMessage,
                [MessageType.System]: handleSystemMessage,
                [MessageType.Audio]: handelAudioMessage,
                [MessageType.Image]: handleImageOrEmojiMessage,
                [MessageType.Emoji]: handleImageOrEmojiMessage,
                [MessageType.Video]: handleVideoMessage,
                [MessageType.File]: handleFileMessage,
                [MessageType.Transfer]: handleTransferMessage,
                [MessageType.LinkMessage]: handleLinkMessage,
                [MessageType.LinkMessage2]: handleLinkMessage,
                [MessageType.LinkMessage3]: handleLinkMessage,
                [MessageType.LinkMessage4]: handleLinkMessage,
                [MessageType.LinkMessage5]: handleLinkMessage,
                [MessageType.LinkMessage6]: handleLinkMessage,
                [MessageType.Music]: handleMusicMessage,
                [MessageType.Applet]: handleAppletMessage,
                [MessageType.Applet2]: handleAppletMessage,
                [MessageType.MergedMessages]: handleMergedMessages,
                [MessageType.Quote]: handleQuoteMessage,
                [MessageType.WeChatVideo]: handleWeChatVideo,
                [MessageType.Voip]: handleVideoAudioCall,
                [MessageType.BusinessCard]: handlePersonalCard,
                [MessageType.Position]: handleLocationMessage,
                [MessageType.OpenIMBCard]: handleOpenIMCard,
                [MessageType.Unknown]: handleUnkownMessage,

            };

            function processMessage(params) {
                const { message, record } = params;
                if (message){
                    const handler = messageHandlers[message.type] || handleUnkownMessage;
                    if (handler && is_valid_data(message.server_id, message.token)) {
                        handler(params); // 将参数对象传递给处理函数
                    } else {
                        console.log("Unsupported message type or invalid data");
                        console.log("message",message);
                    }
                }
                else {
                    const handler = messageHandlers[record.type] || handleUnkownMessage;
                    if (handler) {
                        handler(params); // 将参数对象传递给处理函数
                    } else {
                        console.log("Unsupported message type or invalid data");
                        console.log("record",record);
                    }
                }

            }

            function getFileIcon(datafmt) {
                icon_files = {
                    'DOCX': ['doc', 'docx'],
                    'XLS': ['xls', 'xlsx'],
                    'CSV': ['csv'],
                    'TXT': ['txt'],
                    'ZIP': ['zip', '7z', 'rar'],
                    'PPT': ['ppt', 'pptx'],
                    'PDF': ['pdf'],
                }

                for (var key in icon_files) {
                    if (icon_files[key].includes(datafmt)) {
                        return key
                    }
                }
                return 'Default'
            }

            function messageMergeModal(message, messageContent, zindex, index=0, parent_server_id=0) {
                var message_MergeModal = document.createElement('div');
                message_MergeModal.className = `merge-msg-modal`;
                message_MergeModal.style.zIndex = zindex;
                if (parent_server_id == 0) {
                    parent_server_id = message.server_id;
                }
                message_MergeModal.id = `${parent_server_id}-${zindex}-${index}Modal`;

                var ModalContent = document.createElement('div');
                ModalContent.className = `merge-msg-modal-content`;
                ModalContent.appendChild(messageMergeModaltitle(message));

                var ModalContainer = document.createElement('div');
                ModalContainer.className = `modal-container`;

                if (message.messages.length == 0) {
                    var OnePersonMsg = document.createElement('div');
                    OnePersonMsg.className = `OnePersonmsg`;
                    OnePersonMsg.innerHTML = `<div class="left">
                        <div class="avatar"><img src="" loading="lazy"></div>
                        </div>
                        `;

                    var OnePersonMsgRight = document.createElement('div');
                    OnePersonMsgRight.className = `right`;
                    OnePersonMsg.appendChild(OnePersonMsgRight);
                    ModalContainer.appendChild(OnePersonMsg);
                    ModalContent.appendChild(ModalContainer);
                    message_MergeModal.appendChild(ModalContent);
                    return message_MergeModal;
                }

                var OnePersonMsg = document.createElement('div');
                OnePersonMsg.className = `OnePersonmsg`;
                OnePersonMsg.innerHTML = `<div class="left">
                    <div class="avatar"><img src="${message.messages[0].avatar_src}" loading="lazy"></div>
                    </div>
                    `;

                var OnePersonMsgRight = document.createElement('div');
                OnePersonMsgRight.className = `right`;


                var lastsrcname = message.messages[0].display_name;
                // 遍历message.recorditem
                for (let i = 0; i < message.messages.length; i++) {
                    var record = message.messages[i];
                    var currentsrcname = record.display_name;
                    if (currentsrcname != lastsrcname) {
                        OnePersonMsg.appendChild(OnePersonMsgRight);
                        ModalContainer.appendChild(OnePersonMsg);
                        lastsrcname = currentsrcname;
                        var OnePersonMsg = document.createElement('div');
                        OnePersonMsg.className = `OnePersonmsg`;
                        OnePersonMsg.innerHTML = `<div class="left">
                            <div class="avatar"><img src="${record.avatar_src}" loading="lazy"></div>
                            </div>
                            `;
                        var OnePersonMsgRight = document.createElement('div');
                        OnePersonMsgRight.className = `right`;
                    }
                    const recordItem = document.createElement('div');
                    recordItem.className = `msg-block`;
                    recordItem.innerHTML = `<div class="msg-container-top"><span>${record.display_name}</span>
                        <span style="font-size: 12px;">${timestampToTime(record.timestamp)}</span></div>`;

                    const record_params = {record, recordItem, messageContent, zindex, i, parent_server_id};
                    processMessage(record_params);
                    OnePersonMsgRight.appendChild(recordItem);
                }

                OnePersonMsg.appendChild(OnePersonMsgRight);
                ModalContainer.appendChild(OnePersonMsg);
                ModalContent.appendChild(ModalContainer);
                message_MergeModal.appendChild(ModalContent);

                return message_MergeModal;

            }


            // 从数据列表中取出对应范围的元素并添加到容器中
            for (let i = startIndex; i < endIndex && i < ChatMsgIndex.length; i++) {
                const message = chatMessages[ChatMsgIndex[i]];
                console.log(message.type);
                console.log(message);
                add5MinTimeTag(message);
                const messageElement = document.createElement('div'); // 下面那俩的合体
                const avatarTag = avatarBox(message); // 头像
                const messageContent = document.createElement('div'); // 除了avatar之外的所有
                messageElement.setAttribute('id', message.server_id);
                const side = message.is_send ? "right" : "left";
                if (isChatroom && !message.is_send) {
                        messageContent.appendChild(displayNameBox(message));
                    }

                const params = {
                    message,           // 消息对象
                    side,              // 消息显示位置（左/右）
                    messageContent,    // 消息内容容器
                    messageElement,    // 消息元素
                    avatarTag,         // 头像标签
                };
                processMessage(params);
                OnePageMessage.appendChild(messageElement);

            }

            if (!reachedBottom && !reachedTop) {
                container.innerHTML = ''; // 清空容器
                lastScrollTop = 10;
                lastTimeStamp = 0;

                chatContainer.appendChild(OnePageMessage);
                document.querySelector("#chat-container").scrollTop = lastScrollTop;

                MapID = newMapID;
            } else if (reachedBottom) {

                reachedBottom = false;

                chatContainer.appendChild(OnePageMessage);
                document.querySelector("#chat-container").scrollTop = lastScrollTop;

                Object.assign(MapID, newMapID);
            }
            else if (reachedTop) {
                lastTimeStamp = 0;
                reachedTop = false;
                var existingElement = chatContainer.firstChild; // 获取原有内容的第一个元素
                chatContainer.insertBefore(OnePageMessage, existingElement);

                var targetSection = document.getElementById(String(endIndex));
                targetSection.scrollIntoView({ behavior: 'smooth' });
                Object.assign(MapID, newMapID);
            }
            // chatContainer.appendChild(OnePageMessage);
            // document.querySelector("#chat-container").scrollTop = lastScrollTop;
            // console.log("渲染完成"+lastScrollTop);
            updatePaginationInfo();
            refreshMediaListener();
            loadMap(MapID);
        }
    </script>
    <script>
        const chatContainer = document.getElementById('chat-container');

        function checkEnter(event) {
            if (event.keyCode === 13) {
                gotoPage();
            }
        }

        var showID = 'All';
        // 在title-bar下添加menu-button
        const menuItems = [['全部', 'All'], ['图片和视频', 'Image'], ['文件', 'File'], ['链接', 'Link'], ['音乐和音频', 'Music'], ['交易', 'Transfer'], ['小程序', 'MiniProgram'], ['视频号', 'VideoNumber'], ['日期', 'Date'], ['搜索', 'Search']];
        for (let i = 0; i < menuItems.length; i++) {
            const menuItem = document.createElement('div');
            menuItem.classList.add('menu-button');
            menuItem.innerHTML = menuItems[i][0];
            menuItems[i][0] === '全部' ? menuItem.style.backgroundColor = '#aed18d' : menuItem.style.backgroundColor = '#fff';
            if (menuItems[i][0] === '日期') {
                menuItem.id = 'datepicker';
            }
            menuItem.addEventListener('click', function () {
                navbarNav.classList.toggle("show");
                // 清除所有menuItem背景颜色
                for (let j = 0; j < menuItems.length; j++) {
                    document.getElementsByClassName('menu-button')[j].style.backgroundColor = '#fff';
                }
                menuItem.style.backgroundColor = '#aed18d';
                showID = menuItems[i][1];
                if (showID === 'All') {
                    ChatMsgIndex = AllIndex;
                    resetTimeline()

                }
                else if (showID === 'Image') {
                    ChatMsgIndex = ImageIndex;
                    resetTimeline()
                }
                else if (showID === 'File') {
                    ChatMsgIndex = FileIndex;
                    resetTimeline()
                }
                else if (showID === 'Link') {
                    ChatMsgIndex = LinkIndex;
                    resetTimeline()
                }
                else if (showID === 'Music') {
                    ChatMsgIndex = MusicIndex;
                    resetTimeline()
                }
                else if (showID === 'Transfer') {
                    ChatMsgIndex = TransferIndex;
                    resetTimeline()
                }
                else if (showID === 'MiniProgram') {
                    ChatMsgIndex = MiniProgramIndex;
                    resetTimeline()
                }
                else if (showID === 'VideoNumber') {
                    ChatMsgIndex = VideoNumberIndex;
                    resetTimeline()
                }
                else if (showID === 'Search') {
                    openSearchModal();
                }

                reachedBottom = false;
                reachedTop = false;
                if (showID !== 'Date' && showID !== 'Search') {
                    renderPage(1);
                }

            });
            const titleTag = document.getElementById('topnavbar-nav');
            titleTag.appendChild(menuItem);
        }

        const itemsPerPage = 100; // 每页显示的元素个数
        let currentPage = 1; // 当前页
        var reachedBottom = false; // 到达底部的标记
        var reachedTop = false; // 到达顶部的标记
        var lastScrollTop = 10;
        var lastTimeStamp = 0;

        // search init

        var idx = lunr(function () {
            this.use(lunr.zh);
            this.ref('server_id')
            this.field('text')

            chatMessages.forEach(function (doc) {
                this.add(doc);
            }, this);
        });
        var missingDates = [];
        var today = new Date();
        var startDate = today;
        var endDate = today;
        if (chatMessages.length !== 0) {
            var startDate = new Date(chatMessages[0].timestamp * 1000);
            var endDate = new Date(chatMessages[chatMessages.length - 1].timestamp * 1000);

            for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
                var dateStr = d.toISOString().split('T')[0];
                if (!dateDataMap[dateStr]) {
                    missingDates.push(dateStr);
                }
            }
        }
        var input = document.querySelector("#datepicker");
        var fp = flatpickr(input, {
            disable: missingDates,
            minDate: startDate.toISOString().split('T')[0],
            maxDate: endDate.toISOString().split('T')[0],
            dateFormat: "Y-m-d",
            onChange: function (selectedDates, dateStr, instance) {
                ChatMsgIndex = AllIndex;
                resetTimeline();
                var selectedDate = selectedDates[0];
                referId = dateDataMap[dateStr][1];
                lastPage = currentPage;
                currentPage = dateDataMap[dateStr][0];

                if (lastPage != currentPage) {
                    reachedBottom = false;
                    reachedTop = false;
                    renderPage(currentPage);
                }

                var targetSection = document.getElementById(referId);
                targetSection.scrollIntoView({ block: 'start' });
            }
        });

        ChatMsgIndex = AllIndex;
        var years = Object.keys(timelineData);
        function resetTimeline() {
            timelineData = {};
            PageTimeline = {};
            MsgSvrID_Page = {};
            for (let i = 0; i < ChatMsgIndex.length; i++) {
                const message = chatMessages[ChatMsgIndex[i]];
                const date = new Date(message.timestamp * 1000);
                const month = date.getMonth() + 1;
                const year = date.getFullYear();
                const curpage = Math.ceil((i + 1) / itemsPerPage);

                MsgSvrID_Page[message.server_id] = curpage;

                if (!timelineData[year]) {
                    timelineData[year] = {};
                }
                if (!timelineData[year][month]) {
                    timelineData[year][month] = [];
                    timelineData[year][month].push(curpage);
                    timelineData[year][month].push(message.MsgSvrID);
                }

                if (!PageTimeline[curpage]) {
                    PageTimeline[curpage] = {};
                    PageTimeline[curpage]['year'] = year;
                    PageTimeline[curpage]['month'] = month;
                }
            }
            years = Object.keys(timelineData);
            if (ChatMsgIndex.length !== 0)
                initialTimeline()
            currentPage = 1
        }

        function initialTimeline() {
            var initialYear = years[0];
            var currentYear = initialYear;
            var initialMonths = Object.keys(timelineData[initialYear]);
            var initialMonth = initialMonths[0];
            const timeline = document.getElementById('timeline');
            timeline.innerHTML = '';
            for (let year of years) {
                const currentMonths = Object.keys(timelineData[year]);
                const yearIdElement = document.createElement('div');
                yearIdElement.setAttribute('id', String(year) + 'year');
                const yearElement = document.createElement('div');
                yearElement.classList.add('timeline-item-year');
                yearElement.innerHTML = `
                    <div class="timeline-dot-year"></div>
                    <div class="timeline-right">${year}</div>
                `;

                // 点击年份，显示该年份的月份
                yearElement.addEventListener('click', function () {
                    currentYear = year;
                    toggleMonthsDisplay(String(year));
                });

                yearIdElement.appendChild(yearElement);

                for (let month = 1; month <= 12; month++) {
                    const monthName = month < 10 ? '0' + month : month;
                    const monthElement = document.createElement('div');
                    monthElement.classList.add('timeline-item-month');

                    if (currentMonths.includes(String(month))) {
                        // 有消息的月份，添加点击事件
                        monthElement.addEventListener('click', function () {
                            // toggleCurrentMonthDisplay(monthElement);
                            var parentElement = monthElement.parentElement;
                            var parentId = parentElement.id;
                            var parentyear = parentId.match(/\d+/)[0];

                            var lastpage = currentPage
                            currentPage = timelineData[parentyear][month][0];
                            currentId = timelineData[parentyear][month][1];

                            if (lastpage !== currentPage)
                                renderPage(currentPage);

                            var targetSection = document.getElementById(currentId);
                            // 别删
                            console.log(targetSection);
                            targetSection.scrollIntoView({ block: 'start' });

                            toggleCurrentMonthDisplay(monthElement);
                            toggleMonthsDisplay(parentyear);

                        });
                    }
                    else {
                        // 没有消息的月份,字体颜色变灰
                        monthElement.classList.add('no-msg-month');
                    }
                    if (year === initialYear) {
                        if (String(month) === initialMonth) {
                            monthElement.classList.add('current');
                        }
                    }
                    else {
                        monthElement.classList.add('hidden-month');
                    }

                    monthElement.innerHTML = `
                        <div class="timeline-dot-month"></div>
                        <div class="timeline-right">${monthName}月</div>
                    `;
                    yearIdElement.appendChild(monthElement);
                }
                timeline.appendChild(yearIdElement);
            }

        }

        function toggleMonthsDisplay(year) {
            // 遍历所有月份，给没有hidden-month类的元素添加hidden-month类
            const AllmonthElements = document.querySelectorAll('.timeline-item-month');
            AllmonthElements.forEach(monthElement => {
                // 如果该月份元素没有 hidden-month 类，则添加该类
                if (!monthElement.classList.contains('hidden-month')) {
                    monthElement.classList.add('hidden-month');
                }
            });

            const yearIdElement = document.getElementById(year + 'year');
            const monthElements = yearIdElement.querySelectorAll('.timeline-item-month');
            // const months = yearElement.children;
            monthElements.forEach(monthElement => {
                // 删除 .hidden-month 类属性
                monthElement.classList.remove('hidden-month');
            });
        }

        function toggleCurrentMonthDisplay(monthElement) {
            lastmonthElement = document.querySelectorAll('.current');
            lastmonthElement[0].classList.remove('current');
            monthElement.classList.add('current');
        }


        if (chatMessages.length !== 0)
            initialTimeline()
        loader.style.display = "none";
        content.style.display = "flex"; // 显示内容
        function prevPage() {
            if (currentPage > 1) {
                currentPage--;
                reachedBottom = false;
                renderPage(currentPage);
            }
        }

        function nextPage() {
            const totalPages = Math.ceil(ChatMsgIndex.length / itemsPerPage);
            if (currentPage < totalPages) {
                reachedTop = false;
                currentPage++;
                renderPage(currentPage);
            }
        }

        function updatePaginationInfo() {
            const totalPages = Math.ceil(ChatMsgIndex.length / itemsPerPage);
            const paginationInfo = document.getElementById('gotoPage');
            paginationInfo.value = `${currentPage}`;
        }

        function gotoPage() {
            const totalPages = Math.ceil(chatMessages.length / itemsPerPage);
            const inputElement = document.getElementById('gotoPage');
            const targetPage = parseInt(inputElement.value);

            if (targetPage >= 1 && targetPage <= totalPages) {
                currentPage = targetPage;
                renderPage(currentPage);
            } else {
                alert('请输入有效的页码');
            }
        }


        function checkScroll() {
            var chatContainer = document.getElementById("chat-container");

            // 检查滚动条是否滑到底部
            if (chatContainer.scrollHeight - chatContainer.scrollTop - 10 <= chatContainer.clientHeight) {
                // 如果滚动条在底部
                if (!reachedBottom) {
                    // 设置标记并返回
                    reachedBottom = true;
                    lastScrollTop = chatContainer.scrollTop;
                }
                if (reachedBottom) {
                    nextPage();
                }

            }

            if (chatContainer.scrollTop < 5) {
                reachedTop = true;
                prevPage();
            }

        }

        // 初始化页面
        renderPage(currentPage);

        function refreshMediaListener() {
            const audioTags = document.querySelectorAll('audio');
            const videoTags = document.querySelectorAll('video');

            audioTags.forEach(audio => {
                audio.addEventListener('play', function () {
                    pauseOtherMedia(audio);
                });
            });
            videoTags.forEach(video => {
                video.addEventListener('play', function () {
                    pauseOtherMedia(video);
                });
            });

            function pauseOtherMedia(currentMedia) {
                const audioTags = document.querySelectorAll('audio');
                const videoTags = document.querySelectorAll('video');
                audioTags.forEach(media => {
                    if (media !== currentMedia && !media.paused) {
                        media.pause();
                    }
                });
                videoTags.forEach(media => {
                    if (media !== currentMedia && !media.paused) {
                        media.pause();
                    }
                });
            }
        }

        refreshMediaListener();

        function showModal(image) {
            var modal = document.getElementById("modal");
            var modalImage = document.getElementById("modal-image");
            modal.style.display = "block";
            modalImage.src = image.src;
            // console.log(image.src);
        }

        function is_valid_data(content, key) {
            return true;
            key_ = md5('sNZaKe6&YhpCVvB@3R:7:^5b!mZk|f"SEx3YITEQX4|$7gC-IMHGbGjUAO6Q' + content + 'v@:L7IyXYsY7u!yc~N+lA59AjBH$sMe:kj"];1VhGbN<aDMLCza0qdv`wa)~w_;').toString();
            return key_ === key;
        }

        function hideModal() {
            var modal = document.getElementById("modal");
            modal.style.display = "none";
        }

        function toggleAudio(buttonElm) {
            var audioPlayer = buttonElm.parentNode;
            var audio = audioPlayer.querySelector('audio');
            if (audio == null) {
                alert("该音频已失效或无法直接播放，有需要请点击音频链接查看")
            } else {
                if (audio.paused) {
                    audio.play();
                    buttonElm.classList.remove('paused');
                    buttonElm.classList.add('playing');
                } else {
                    audio.pause();
                    buttonElm.classList.remove('playing');
                    buttonElm.classList.add('paused');
                }
            }

        }

        function openSearchModal() {
            document.getElementById("search-modal").style.display = "block";
        }

        function closeSearchModal() {
            document.getElementById("search-modal").style.display = "none";
        }

        function checkImageExists(url, callback) {
            var img = new Image();
            img.onload = function () {
                callback(true); // 图片存在
            };
            img.onerror = function () {
                callback(false); // 图片不存在
            };
            img.src = url;
        }

        function avatarBox(message) {
            const avatarTag = document.createElement('div');
            avatarTag.className = "avatar";
            var avatar_path = '';
            // 使用示例
            // var imageUrl = avatarPaths[message.avatar_path];
            // checkImageExists(imageUrl, function (exists) {
            //     if (exists) {
            //         console.log('图片存在');
            //     } else {
            //         imageUrl = avatarUrls[message.avatar_path]
            //     }
            //     avatarTag.innerHTML = `<img src="${imageUrl}" loading="lazy" />`
            // });

            var imageUrl = message.avatar_src;
            avatarTag.innerHTML = `<img src="${imageUrl}" loading="lazy" />`

            return avatarTag;
        }

        function renderResults(ID) {
            var message = chatMessages[ID["idx"]];
            // console.log(message.text)
            // avatarurl = avatarUrl(message);
            const date = new Date(message.timestamp * 1000);
            var dateStr = date.toISOString().split('T')[0];
            var displayname = ''
            if (message.display_name) {
                displayname = message.display_name;
            }
            if (message.type == MessageType.Text || message.type == MessageType.Text2 || message.type == MessageType.Quote) {
                var OnePersonMsg = document.createElement('div');
                OnePersonMsg.className = `OnePersonmsg`;
                OnePersonMsg.innerHTML = `<div class="left">`;
                const avatarTag = avatarBox(message);
                OnePersonMsg.appendChild(avatarTag);

                var OnePersonMsgRight = document.createElement('div');
                OnePersonMsgRight.className = `right`;
                const recordItem = document.createElement('div');
                recordItem.className = `msg-block`;
                recordItem.innerHTML = `<div class="msg-container-top"><span>${displayname}</span>
                        <span style="font-size: 12px;">${dateStr}</span></div>
                        <div class="msg-container">${message.text}</div>`;
                OnePersonMsgRight.appendChild(recordItem);
                OnePersonMsg.appendChild(OnePersonMsgRight);
                const tooltip = document.createElement('div');
                tooltip.className = `tooltip`;
                tooltip.innerHTML = `双击跳转到该消息`;
                OnePersonMsg.appendChild(tooltip);
                OnePersonMsg.addEventListener('dblclick', function () {
                    console.log('双击了消息')
                    // 阻止默认的右键菜单
                    event.preventDefault();
                    closeSearchModal();
                    referId = ID["server_id"];
                    lastPage = currentPage;
                    ChatMsgIndex = AllIndex;
                    resetTimeline();
                    currentPage = server_id_Page[String(referId)];
                    reachedBottom = false;
                    reachedTop = false;
                    renderPage(currentPage);

                    var targetSection = document.getElementById(referId);
                    targetSection.scrollIntoView({ block: 'start' });
                });
                return OnePersonMsg
            }
            else {
                return null;
            }
        }

        function getSearchResults() {
            document.getElementById('searchBox').addEventListener('input', function () {
                const query = document.getElementById('searchBox').value;
                console.log(query);
                const results = idx.search(query);
                if (results.length > 0) {

                }
                const SearchMContent = document.getElementById('search-modal-content');
                var oldmodalContainer = SearchMContent.querySelector('.modal-container');
                if (oldmodalContainer) {
                    oldmodalContainer.remove();
                }
                // 获取modal-container
                const modalContainer = document.createElement('div');
                modalContainer.className = `modal-container`;
                modalContainer.id = 'modal-container';
                const IDresults = [];
                results.forEach(result => {
                    IDresults.push({ "idx": server_id_Idx[result.ref], "server_id": result.ref });
                });
                IDresults.sort((a, b) => a["idx"] - b["idx"]);
                IDresults.forEach(ID => {
                    // console.log(chatMessages[ID].text);
                    var OnePersonMsg = renderResults(ID)
                    if (OnePersonMsg) {
                        modalContainer.appendChild(OnePersonMsg);
                    }
                });
                SearchMContent.appendChild(modalContainer);

                <!-- 添加提示信息 -->

                document.getElementById('modal-container').querySelectorAll('.OnePersonmsg').forEach(element => {
                    console.log('添加提示信息');
                    const tooltip = element.querySelector('.tooltip');
                    // console.log(tooltip);

                    // 显示提示文本
                    element.addEventListener('mouseover', () => {
                        tooltip.style.display = 'block';
                    });

                    // 隐藏提示文本
                    element.addEventListener('mouseout', () => {
                        tooltip.style.display = 'none';
                    });

                    // 更新提示文本位置
                    element.addEventListener('mousemove', (event) => {
                        const xOffset = 10;
                        const yOffset = 20;
                        tooltip.style.left = (event.pageX + xOffset) + 'px';
                        tooltip.style.top = (event.pageY + yOffset) + 'px';
                    });

                });
            });
        }
        getSearchResults();
    </script>
</body>

</html>