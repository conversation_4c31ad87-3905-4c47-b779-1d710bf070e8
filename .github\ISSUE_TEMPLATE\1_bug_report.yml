name: '🐛 反馈缺陷 Bug Report'
description: '反馈一个问题缺陷 | Report an bug'
title: '[Bug] '
labels: ['🐛 Bug']
body:
  - type: dropdown
    attributes:
      label: '👌 是否需要解答'
      options:
        - 否
        - 是
    validations:
      required: true
  - type: dropdown
    attributes:
      label: '👌 是否检查过没有类似issue'
      options:
        - 否
        - 是
    validations:
      required: true
  - type: dropdown
    attributes:
      label: '💻 Python版本'
      options:
        - 3.12
        - 3.11
        - 3.10
        - Other
        - 什么是Python
        - 我用的EXE程序
  - type: dropdown
    attributes:
      label: '💻 微信版本'
      options:
        - 最新版
        - Other(更新到最新版再试试)
  - type: dropdown
    attributes:
      label: '软件版本 | EXE Version'
      options:
        - 我是找茬的
        - 最新版
        - Other
        - 源码
    validations:
      required: true
  - type: textarea
    attributes:
      label: '🐛 问题描述 | Bug Description'
      description: A clear and concise description of the bug.
      value: '报错截图:'
    validations:
      required: true
  - type: textarea
    attributes:
      label: '🚦 期望结果 | Expected Behavior'
      description: A clear and concise description of what you expected to happen.
  - type: textarea
    attributes:
      label: '📷 复现步骤 | Recurrence Steps'
      description: A clear and concise description of how to recurrence.
  - type: textarea
    attributes:
      label: '📝 补充信息 | Additional Information'
      description: If your problem needs further explanation, or if the issue you're seeing cannot be reproduced in a gist, please add more information here.
