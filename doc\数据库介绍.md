# 微信数据库介绍

**文章来源：[https://github.com/xaoyaoo/PyWxDump/blob/master/doc/wx%E6%95%B0%E6%8D%AE%E5%BA%93%E7%AE%80%E8%BF%B0.md](https://github.com/xaoyaoo/PyWxDump/blob/master/doc/wx%E6%95%B0%E6%8D%AE%E5%BA%93%E7%AE%80%E8%BF%B0.md)**

转载请注明原始出处

# 微信PC端各个数据库简述

* 说明：针对 .../WeChat Files/wxid_xxxxxxxx/Msg下的各个文件解密后的内容进行概述
* 未作特别说明的情况下，“聊天记录数据”指代的数据结构上都和Multi文件夹中的完整聊天记录数据相同或类似。
* 本文档仅供学习交流使用，严禁用于商业用途及非法用途，否则后果自负

## 一、微信小程序相关

微信小程序的相关数据，包括但不限于：

* 你使用过的小程序 RecentWxApp
* 星标的小程序 StarWxApp
* 各个小程序的基本信息 WAContact

用处不大，不过可以看到你使用过的小程序的名称和图标，以及小程序的AppID

## 二、企业微信相关

### BizChat

企业微信联系人数据，包括但不限于：

* 在微信中可以访问的企业微信会话ChatInfo
* 一部分会话的信息ChatSession（未确认与ChatInfo的关系；这其中的Content字段是最近一条消息，疑似用于缓存展示的内容）
* 包括群聊在内的聊天涉及的所有企业微信用户身份信息UsrInfo
* 该微信账号绑定的企业微信身份MyUsrInfo
* 特别说明：未经详细查证，这其中的聊天是否包含使用普通微信身份与企业微信用户发起的聊天，还是只包含使用绑定到普通微信的企业微信身份与其它企业微信身份发起的聊天。

### BizChatMsg

* 企业微信聊天记录数据，包括所有和企业微信聊天的数据。
* 与BizChat一样，未确定涉及的范围究竟是只有企业微信-企业微信还是同时包含普通微信-企业微信。
* 另外，此处的消息与Multi文件夹中真正的微信消息不同的是在于没有拆分数据库。

### OpenIM 前缀

* 这个也是企业微信的数据，包括联系人、企业信息、与企业微信联系人的消息等。
* 这个是普通微信-企业微信的数据，上面biz前缀的是企业微信-企业微信
* 这个不常用，而且也没有全新的数据结构，不再详细说了。

### PublicMsg

* 看起来像是企业微信的通知消息，可以理解为企业微信的企业应用消息

## 三、微信功能相关

### Emotion

顾名思义表情包相关，包括但不限于以下内容：

* CustomEmotion：顾名思义用户手动上传的GIF表情，包含下载链接，不过看起来似乎有加密（内有aesKey字段但我没测试）
* EmotionDes1 和 EmotionItem 应该也是类似的内容，没仔细研究
* EmotionPackageItem：账号添加的表情包的集合列表（从商店下载的那种）

ps：用处不大，微信的MSG文件夹有表情包的url链接，可以直接网络获取聊天记录中的表情包。

### Favorite

* FavItems：收藏的消息条目列表
* FavDataItem：收藏的具体数据。没有自习去看他的存储逻辑，不过大概可以确定以下两点
    * 即使只是简单收藏一篇公众号文章也会在 FavDataItem 中有一个对应的记录
    * 对于收藏的合并转发类型的消息，合并转发中的每一条消息在 FavDataItem 中都是一个独立的记录
* FavTags：为收藏内容添加的标签

### Misc

* 有BizContactHeadImg和ContactHeadImg1两张表，应该是二进制格式的各个头像

### Sns

微信朋友圈的相关数据：

* FeedsV20：朋友圈的XML数据
* CommentV20：朋友圈点赞或评论记录
* NotificationV7：朋友圈通知
* SnsConfigV20：一些配置信息，能读懂的是其中有你的朋友圈背景图
* SnsGroupInfoV5：猜测是旧版微信朋友圈可见范围的可见或不可见名单

### FTS（搜索）

* 前缀为 FTS 的数据库可能都和全文搜索（Full-Text Search）相关(就是微信那个搜索框)

### FTSContact

有一堆表

* FTSChatroom15_content 和 FTSContact15_content
  分别对应的是微信“聊天”界面会展示的消息会话（包括公众号等）和“联系人”界面会出现的所有人（有的时候并不是所有联系人都会出现在“聊天”中），信息包含昵称、备注名和微信号，也和微信支持搜索的字段相匹配。

### FTSFavorite

搜索收藏内容的索引

* 命名方式类似上面一条

ps：对于收藏内容通过文字搜索，电脑版是把所有东西拼接成一个超长字符串来实现的。这对于文本、链接等没啥问题，但是对于合并转发消息，就会出现搜索\[图片]
这一关键词。

### MultiSearchChatMsg

* 这个数据库前缀不一样，但是看内容和结构应该还是一个搜索相关，搜索的是聊天记录中的文件
* 存储了文件名和其所在的聊天
* 不过FTSMsgSearch18_content和SessionAttachInfo两张表记录数量有显著差异，不确定是哪个少了或是怎样。

### HardLink（文件在磁盘存储的位置）

* 将文件/图片/视频的文件名指向保存它们的文件夹名称（例如2023-04），有用但不多。

### Media

* ChatCRVoice和MediaInfo 可能为语音信息

## 三、MicroMsg （联系人核心）

一个数据库，不应该和分类平级，但是我认为这是分析到目前以来最核心的，因此单独来说了。

### AppInfo（表）

一些软件的介绍，猜测可能是关于某些直接从手机APP跳转到微信的转发会带有的转发来源小尾巴的信息

### Biz 前缀

与公众号相关的内容，应该主要是账号本身相关。

能确定的是 BizSessionNewFeeds 这张表保存的是订阅号大分类底下的会话信息，包括头像、最近一条推送等。

### ChatInfo

保存“聊天”列表中每个会话最后一次标记已读的时间

### ChatRoom 和 ChatRoomInfo

存储群聊相关信息

* ChatRoom：存储每个群聊的用户列表（包括微信号列表和群昵称列表）和个人群昵称等信息
* ChatRoomInfo：群聊相关信息，主要是群公告内容，与成员无关
  顺便再吐槽一下，微信这个位置有一个命名出现异常的，别的表前缀都是ChatRoom，而突然出现一个ChatroomTool

### Contact

顾名思义，联系人。不过这里的联系人并不是指你的好友，而是所有你可能看见的人，除好友外还有所有群聊中的所有陌生人。

* Contact：这张表存储的是用户基本信息，包括但不限于微信号（没有好友的陌生人也能看！）、昵称、备注名、设置的标签等等，甚至还有生成的各种字段的拼音，可能是用于方便搜索的吧
* ContactHeadImgUrl：头像地址
* ContactLabel：好友标签 ID 与名称对照
* ExtraBuf: 存储位置信息、手机号、邮箱等信息

### PatInfo

存了一部分好友的拍一拍后缀，但是只有几个，我记得我电脑上显示过的拍一拍似乎没有这么少？

### Session

真正的“聊天”栏目显示的会话列表，一个不多一个不少，包括“折叠的群聊”这样子的特殊会话；信息包括名称、未读消息数、最近一条消息等

### TicketInfo

这张表在我这里有百余条数据，但是我实在没搞明白它是什么

## 四、FTSMSG

FTS 这一前缀了——这代表的是搜索时所需的索引。

其内主要的内容是这样的两张表：

* FTSChatMsg2_content：内有三个字段
    * docid：从1开始递增的数字，相当于当前条目的 ID
    * c0content：搜索关键字（在微信搜索框输入的关键字被这个字段包含的内容可以被搜索到）
    * c1entityId：尚不明确用途，可能是校验相关
* FTSChatMsg2_MetaData
    * docid：与FTSChatMsg2_content表中的 docid 对应
    * msgId：与MSG数据库中的内容对应
    * entityId：与FTSChatMsg2_content表中的 c1entityId 对应
    * type：可能是该消息的类型
    * 其余字段尚不明确

特别地，表名中的这个数字2，个人猜测可能是当前数据库格式的版本号。

## 五、MediaMSG （语音消息）

这里存储了所有的语音消息。数据库中有且仅有Media一张表，内含三个有效字段：

* Key
* Reserved0 与MSG数据库中消息的MsgSvrID一一对应
* Buf silk格式的语音数据

## 六、MSG（聊天记录核心数据库）

内部主要的两个表是`MSG`和`Name2ID`

### Name2ID

* `Name2ID`这张表只有一列，内容格式是微信号或群聊ID@chatroom
* 作用是使MSG中的某些字段与之对应。虽然表中没有 ID 这一列，但事实上微信默认了第几行 ID 就是几（从1开始编号）。

### MSG

* localId：字面意思消息在本地的 ID，暂未发现其功用
* TalkerId：消息所在房间的 ID（该信息为猜测，猜测原因见 StrTalker 字段），与Name2ID对应。
* MsgSvrID：猜测 Srv 可能是 Server 的缩写，代指服务器端存储的消息 ID
* Type：消息类型，具体对照见表1
* SubType：消息类型子分类，暂时未见其实际用途
* IsSender：是否是自己发出的消息，也就是标记消息展示在对话页左边还是右边，取值0或1
* CreateTime：消息创建时间的秒级时间戳。此处需要进一步实验来确认该时间具体标记的是哪个时间节点，个人猜测的规则如下：
    * 从这台电脑上发出的消息：标记代表的是每个消息点下发送按钮的那一刻
    * 从其它设备上发出的/收到的来自其它用户的消息：标记的是本地从服务器接收到这一消息的时间
* Sequence：次序，虽然看起来像一个毫秒级时间戳但其实不是。这是`CreateTime`
  字段末尾接上三位数字组成的，通常情况下为000，如果在出现两条`CreateTime`
  相同的消息则最后三位依次递增。需要进一步确认不重复范围是在一个会话内还是所有会话。`CreateTime`
  相同的消息则最后三位依次递增。需要进一步确认不重复范围是在一个会话内还是所有会话。
* StatusEx、FlagEx、Status、MsgServerSeq、MsgSequence：这五个字段个人暂时没有分析出有效信息
* StrTalker：消息发送者的微信号。特别说明，从这里来看的话，上面的`TalkerId`
  字段大概率是指的消息所在的房间ID，而非发送者ID，当然也可能和`TalkerId`属于重复内容，这一点待确认。
* StrContent：字符串格式的数据。特别说明的是，除了文本类型的消息外，别的大多类型这一字段都会是一段 XML
  数据标记一些相关信息。通过解析xml可以得到更多的信息，例如图片的宽高、语音的时长等等。
* DisplayContent：对于拍一拍，保存拍者和被拍者账号信息
* Reserved0~6：这些字段也还没有分析出有效信息，也有的字段恒为空
* CompressContent：字面意思是压缩的数据，实际上也就是微信任性不想存在 StrContent
  里的数据在这里（例如带有引用的文本消息等；采用lz4压缩算法压缩）
* BytesExtra：额外的二进制格式数据
* BytesTrans：目前看这是一个恒为空的字段

表1：MSG.Type字段数值与含义对照表（可能可以扩展到其它数据库中同样标记消息类型这一信息的字段）

| 分类`Type` | 子分类`SubType` | 对应类型                                                        |
|----------|--------------|-------------------------------------------------------------|
| 1        | 0            | 文本                                                          |
| 3        | 0            | 图片                                                          |
| 34       | 0            | 语音                                                          |
| 43       | 0            | 视频                                                          |
| 47       | 0            | 动画表情（第三方开发的表情包）                                             |
| 49       | 1            | 类似文字消息而不一样的消息，目前只见到一个阿里云盘的邀请注册是这样的。估计和57子类的情况一样             |
| 49       | 5            | 卡片式链接，CompressContent 中有标题、简介等，BytesExtra 中有本地缓存的封面路径       |
| 49       | 6            | 文件，CompressContent 中有文件名和下载链接（但不会读），BytesExtra 中有本地保存的路径    |
| 49       | 8            | 用户上传的 GIF 表情，CompressContent 中有CDN链接，不过似乎不能直接访问下载           |
| 49       | 19           | 合并转发的聊天记录，CompressContent 中有详细聊天记录，BytesExtra 中有图片视频等的缓存    |
| 49       | 33/36        | 分享的小程序，CompressContent 中有卡片信息，BytesExtra 中有封面缓存位置           |
| 49       | 57           | 带有引用的文本消息（这种类型下 StrContent 为空，发送和引用的内容均在 CompressContent 中） |
| 49       | 63           | 视频号直播或直播回放等                                                 |
| 49       | 87           | 群公告                                                         |
| 49       | 88           | 视频号直播或直播回放等                                                 |
| 49       | 2000         | 转账消息（包括发出、接收、主动退还）                                          |
| 49       | 2003         | 赠送红包封面                                                      |
| 10000    | 0            | 系统通知（居中出现的那种灰色文字）                                           |
| 10000    | 4            | 拍一拍                                                         |
| 10000    | 8000         | 系统通知（特别包含你邀请别人加入群聊）                                         |